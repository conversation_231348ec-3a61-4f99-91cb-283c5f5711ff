<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>KOTH UI</title>
  <link rel="stylesheet" href="style.css">
</head>
<body>
  <div id="overlay">
    <!-- Enhanced Team Select Screen -->
    <div id="team-select">
      <div class="team-select-container">
        <div class="team-select-header">
          <h1 class="team-select-title">Choose Your Team</h1>
          <div class="team-select-subtitle">Select a team to join the battle</div>
        </div>
        
        <div class="team-buttons-container">
          <div class="team-card" data-team="red">
            <div class="team-card-inner">
              <div class="team-color-indicator red-indicator"></div>
              <div class="team-info">
                <div class="team-name">RED TEAM</div>
                <div class="team-count-display">
                  <span class="team-count" id="count-red">0</span>
                  <span class="team-count-label">PLAYERS</span>
                </div>
              </div>
              <div class="team-select-btn">JOIN</div>
            </div>
          </div>

          <div class="team-card" data-team="blue">
            <div class="team-card-inner">
              <div class="team-color-indicator blue-indicator"></div>
              <div class="team-info">
                <div class="team-name">BLUE TEAM</div>
                <div class="team-count-display">
                  <span class="team-count" id="count-blue">0</span>
                  <span class="team-count-label">PLAYERS</span>
                </div>
              </div>
              <div class="team-select-btn">JOIN</div>
            </div>
          </div>

          <div class="team-card" data-team="green">
            <div class="team-card-inner">
              <div class="team-color-indicator green-indicator"></div>
              <div class="team-info">
                <div class="team-name">GREEN TEAM</div>
                <div class="team-count-display">
                  <span class="team-count" id="count-green">0</span>
                  <span class="team-count-label">PLAYERS</span>
                </div>
              </div>
              <div class="team-select-btn">JOIN</div>
            </div>
          </div>
        </div>

        <div class="team-select-footer">
          <div class="loading-indicator">
            <div class="loading-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
            <div class="loading-text">Loading team data...</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Dynamic Menus: Vehicles & Classes -->
    <div id="menu-container">
      <button id="close-btn">×</button>
      <h2 id="menu-title"></h2>
      <div id="items"></div>
    </div>

    <!-- Weapons Shop UI -->
    <div id="weapons-shop">
      <div class="shop-header">
        <div class="shop-title">WEAPONS SHOP</div>
        <div class="shop-money">$<span id="player-money">7000</span></div>
      </div>

      <div class="shop-search">
        <input type="text" id="weapon-search" placeholder="Search for an item">
        <button class="refresh-btn">🔄</button>
      </div>

      <div class="weapons-grid" id="weapons-grid">
        <!-- Weapons will be populated here -->
      </div>

      <button class="shop-close" id="shop-close">×</button>
    </div>

    <!-- Vehicles Shop UI -->
    <div id="vehicles-shop">
      <div class="shop-header">
        <div class="shop-title">VEHICLES SHOP</div>
        <div class="shop-money">$<span id="vehicle-player-money">7000</span></div>
      </div>

      <div class="shop-search">
        <input type="text" id="vehicle-search" placeholder="Search for a vehicle">
        <button class="refresh-btn">🔄</button>
      </div>

      <div class="vehicles-grid" id="vehicles-grid">
        <!-- Vehicles will be populated here -->
      </div>

      <button class="shop-close" id="vehicle-shop-close">×</button>
    </div>

    <!-- NEW Classes Selection UI - Matching Your Design -->
    <div id="classes-selection">
      <div class="classes-header">
        <h2 class="classes-title">Select a class</h2>
        <button class="classes-close" id="classes-close">×</button>
      </div>

      <div class="classes-grid">
        <div class="class-card" data-class="assault">
          <div class="class-image-container">
            <img src="images/classes/assault.png" alt="Assault" class="class-image">
          </div>
          <div class="class-info">
            <h3 class="class-name">Assault</h3>
            <p class="class-unlock">Unlocked</p>
          </div>
        </div>

        <div class="class-card" data-class="medic">
          <div class="class-image-container">
            <img src="images/classes/medic.png" alt="Medic" class="class-image">
            <div class="class-lock-overlay">
              <span class="lock-icon">🔒</span>
            </div>
          </div>
          <div class="class-info">
            <h3 class="class-name">Medic</h3>
            <p class="class-unlock">Unlock at level 5</p>
          </div>
        </div>

        <div class="class-card" data-class="engineer">
          <div class="class-image-container">
            <img src="images/classes/engineer.png" alt="Engineer" class="class-image">
            <div class="class-lock-overlay">
              <span class="lock-icon">🔒</span>
            </div>
          </div>
          <div class="class-info">
            <h3 class="class-name">Engineer</h3>
            <p class="class-unlock">Unlock at level 15</p>
          </div>
        </div>

        <div class="class-card" data-class="heavy">
          <div class="class-image-container">
            <img src="images/classes/heavy.png" alt="Heavy" class="class-image">
            <div class="class-lock-overlay">
              <span class="lock-icon">🔒</span>
            </div>
          </div>
          <div class="class-info">
            <h3 class="class-name">Heavy</h3>
            <p class="class-unlock">Unlock at level 25</p>
          </div>
        </div>

        <div class="class-card" data-class="scout">
          <div class="class-image-container">
            <img src="images/classes/scout.png" alt="Scout" class="class-image">
            <div class="class-lock-overlay">
              <span class="lock-icon">🔒</span>
            </div>
          </div>
          <div class="class-info">
            <h3 class="class-name">Scout</h3>
            <p class="class-unlock">Unlock at level 40</p>
          </div>
        </div>
      </div>
    </div>

    <!-- NEW Weapon Shop UI - Matching Your Design -->
    <div id="weapon-shop-new">
      <div class="weapon-shop-header">
        <h2 class="weapon-shop-title"><span id="selected-class-name">Assault</span> Class Shop</h2>
        <!-- Display the player’s current class level next to their money.  This element
             will be updated by the UI script when the shop is shown.  -->
        <div class="weapon-shop-level">LVL <span id="weapon-shop-level">0</span></div>
        <div class="weapon-shop-money">$ <span id="weapon-shop-money">2,100</span></div>
        <button class="weapon-shop-close" id="weapon-shop-close">×</button>
      </div>

      <div class="weapon-categories">
        <button class="weapon-category active" data-category="primary">Primary</button>
        <button class="weapon-category" data-category="secondary">Secondary</button>
        <button class="weapon-category" data-category="special">Special</button>
        <button class="weapon-category" data-category="throwable">Throwable</button>
      </div>

      <div class="weapon-grid" id="weapon-grid">
        <!-- Weapons will be dynamically populated here -->
      </div>
    </div>
  </div>

  <!-- PERMANENT GAME HUD - COMPACT GREY DESIGN (SMALLER) -->
  <div id="game-hud">
    <!-- Zone Control Points (Top Section) -->
    <div class="zone-points">
      <div class="zone-box red-zone">
        <div class="zone-number" id="red-zone-points">0</div>
        <div class="zone-label">RED</div>
      </div>

      <div class="zone-box green-zone">
        <div class="zone-number" id="green-zone-points">0</div>
        <div class="zone-label">GREEN</div>
      </div>

      <div class="zone-box blue-zone">
        <div class="zone-number" id="blue-zone-points">0</div>
        <div class="zone-label">BLUE</div>
      </div>
    </div>

    <!-- Player Info Section -->
    <div class="player-info">
      <div class="player-name" id="player-name">andyflip9</div>
      <div class="player-money" id="player-money-display">$49,235</div>
      <!-- VIP bonus indicator.  This element is shown when the player
           possesses the configured VIP Discord role.  The UI script
           toggles this element based on data.isVip. -->
      <div class="vip-indicator" id="vip-bonus-indicator" style="display: none;">
        <span class="vip-money-icon">💰</span>
        <span class="vip-multiplier">1.5x Money</span>
        <span class="separator">&amp;</span>
        <span class="vip-xp-icon">⭐</span>
        <span class="vip-multiplier">1.5x XP</span>
      </div>
      <div class="player-stats">
        <div class="stat-item">
          <div class="stat-label">Level</div>
          <div class="stat-value" id="player-level">41</div>
        </div>
        <!-- Display the current class level in place of the kill count.  The value
             updates whenever the player selects a different class or gains
             class XP. -->
        <div class="stat-item">
          <div class="stat-label">Class Level</div>
          <div class="stat-value" id="player-class-level">0</div>
        </div>
      </div>

      <!-- XP Progress Bar -->
      <div class="xp-progress">
        <div class="xp-bar">
          <div class="xp-fill" id="xp-fill" style="width: 63%;"></div>
        </div>
        <div class="xp-text" id="xp-text">2847 / 4500 XP</div>
      </div>

      <!-- Team Player Counts -->
      <div class="team-player-counts">
        <div class="team-players-item red">
          <span class="team-dot red-dot"></span>
          <span class="team-count-text" id="red-players">0</span>
        </div>
        <div class="team-players-item green">
          <span class="team-dot green-dot"></span>
          <span class="team-count-text" id="green-players">0</span>
        </div>
        <div class="team-players-item blue">
          <span class="team-dot blue-dot"></span>
          <span class="team-count-text" id="blue-players">0</span>
        </div>
      </div>
    </div>

    <!-- Health Bar REMOVED as requested - user will integrate their own later -->

    <!-- KOTH Zone Status REMOVED - Now uses chat messages -->
  </div>

  <!-- DEATH SCREEN -->
  <div id="death-screen">
    <div class="death-content">
      <!-- Updated death message to be less aggressive -->
      <div class="death-title">You Died</div>

      <div class="respawn-section">
        <div class="respawn-instruction">HOLD <span class="key-highlight">E</span> TO RESPAWN</div>
        <div class="respawn-progress">
          <div class="respawn-bar">
            <div class="respawn-fill" id="respawn-fill"></div>
          </div>
        </div>
      </div>

      <div class="bleedout-section">
        <div class="bleedout-text">BLEEDING OUT IN <span id="bleedout-timer">50</span> SECONDS!</div>
        <div class="medic-call">QUICK! Shout <span class="medic-highlight">MEDIC!</span></div>
      </div>

      <div class="killer-info">
        <div class="killer-text">Killed by: <span class="killer-id">[<span id="killer-id">463426</span>]</span> <span class="killer-name" id="killer-name">Jeon Kim</span></div>
      </div>
    </div>
  </div>

  <!-- KILL FEED CONTAINER -->
  <!-- This container holds kill feed messages and is positioned
       in the top‑right corner of the screen.  Messages are added
       dynamically via the UI script and fade out after a few
       seconds. -->
  <div id="kill-feed-container"></div>

  <!-- ENHANCED KILL REWARD POPUP -->
  <div id="kill-reward-popup">
    <div class="kill-reward-content">
      <div class="kill-reward-header">
        <div class="kill-reward-plus">+</div>
        <div class="kill-reward-money" id="kill-reward-value">$350</div>
        <div class="kill-reward-confirmed">Kill confirmed</div>
      </div>
      <div class="kill-reward-details">
        <div class="kill-reward-xp">
          <span class="xp-plus">+</span>
          <span class="xp-amount" id="kill-xp-value">300</span>
          <span class="xp-label">xp</span>
        </div>
        <div class="kill-reward-zone" id="kill-reward-zone-indicator" style="display: none;">
          <span class="zone-text">ZONE KILL BONUS!</span>
        </div>
      </div>
    </div>
  </div>

  <!-- LEVEL UP POPUP -->
  <div id="levelup-popup">
    <div class="levelup-content-small">
      <div class="levelup-header">
        <span class="levelup-icon">⭐</span>
        <span class="levelup-title">LEVEL UP!</span>
      </div>
      <div class="levelup-progress">
        <span class="level-old" id="level-old">1</span>
        <span class="level-arrow">→</span>
        <span class="level-new" id="level-new">2</span>
      </div>
    </div>
  </div>

  <!-- WEAPON HOTBAR -->
  <div id="weapon-hotbar">
    <div class="hotbar-slot" data-slot="1">
      <div class="slot-number">1</div>
      <div class="weapon-icon"></div>
      <div class="ammo-count"></div>
    </div>
    <div class="hotbar-slot" data-slot="2">
      <div class="slot-number">2</div>
      <div class="weapon-icon"></div>
      <div class="ammo-count"></div>
    </div>
    <div class="hotbar-slot" data-slot="3">
      <div class="slot-number">3</div>
      <div class="weapon-icon"></div>
      <div class="ammo-count"></div>
    </div>
    <div class="hotbar-slot" data-slot="4">
      <div class="slot-number">4</div>
      <div class="weapon-icon"></div>
      <div class="ammo-count"></div>
    </div>
    <div class="hotbar-slot" data-slot="5">
      <div class="slot-number">5</div>
      <div class="weapon-icon"></div>
      <div class="ammo-count"></div>
    </div>
  </div>

  <script src="script.js"></script>
  <script src="script_ui_fix.js"></script>
  <script src="script_hud_fix.js"></script>
  <script src="script_death.js"></script>
  <script src="script_ui_management_fix.js"></script>
  <script src="script_shop_fix.js"></script>
  <script src="script_koth_fix.js"></script>
  <script src="script_new_ui.js"></script>
</body>
</html>
