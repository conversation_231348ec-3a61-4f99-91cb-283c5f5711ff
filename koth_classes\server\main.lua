-- Server-side class system
local playerClasses = {}
-- Track per-player class levels and XP in memory.  This table is keyed by
-- server ID and stores a dictionary mapping class names (e.g. 'assault') to
-- their current level and XP.  Data is loaded from the database when
-- players join and updated whenever XP is awarded.  See the
-- `koth_player_class_levels` table creation below for schema details.
local playerClassLevels = {}
local activeMedBags = {}
local medBagIdCounter = 0

-- Debug print function
local function debugPrint(...)
    if Config.Debug then
        print('[KOTH Classes Server]', ...)
    end
end

-- Initialize database tables
MySQL.ready(function()
    -- Add player_class column if it doesn't exist
    MySQL.Async.execute([[
        ALTER TABLE koth_players 
        ADD COLUMN IF NOT EXISTS player_class VARCHAR(50) DEFAULT NULL
    ]], {}, function(rowsChanged)
        debugPrint('Database initialized - player_class column ready')
    end)
    
    -- Create cooldowns table
    MySQL.Async.execute([[
        CREATE TABLE IF NOT EXISTS koth_class_cooldowns (
            id INT AUTO_INCREMENT PRIMARY KEY,
            txid VARCHAR(50) NOT NULL,
            ability_slot INT NOT NULL,
            cooldown_end BIGINT NOT NULL,
            UNIQUE KEY unique_player_ability (txid, ability_slot)
        )
    ]], {}, function(rowsChanged)
        debugPrint('Database initialized - cooldowns table ready')
    end)

    -- Create per-class XP/level table if it doesn't exist.  This table
    -- stores each player's XP and level for every class they have played.
    -- The combination of txid and class serves as the primary key.  XP
    -- accumulates from kills, captures or other gameplay actions and
    -- determines when the class level increases.  Levels start at 0.
    MySQL.Async.execute([[CREATE TABLE IF NOT EXISTS koth_player_class_levels (
        txid VARCHAR(50) NOT NULL,
        class VARCHAR(20) NOT NULL,
        level INT DEFAULT 0,
        xp INT DEFAULT 0,
        PRIMARY KEY (txid, class)
    )]], {}, function(rowsChanged)
        debugPrint('Database initialized - class XP/level table ready')
    end)
end)

-- Get player identifier
local function getPlayerIdentifier(source)
    local identifiers = GetPlayerIdentifiers(source)
    for _, id in ipairs(identifiers) do
        if string.find(id, "license:") then
            return id
        end
    end
    return nil
end

-- Load a player's class levels and XP from the database.  Called when a
-- player joins and when their class is first requested.  This ensures
-- that class XP/level data is available in memory for quick access.
local function loadClassLevels(source)
    local txid = getPlayerIdentifier(source)
    if not txid then return end
    playerClassLevels[source] = {}
    -- Fetch all class records for this player
    local rows = MySQL.Sync.fetchAll('SELECT class, level, xp FROM koth_player_class_levels WHERE txid = @txid', {
        ['@txid'] = txid
    }) or {}
    for _, row in ipairs(rows) do
        playerClassLevels[source][row.class] = { level = tonumber(row.level) or 0, xp = tonumber(row.xp) or 0 }
    end
end

-- Get a player's class level from memory; if not loaded, return 0
local function getClassLevel(source, classId)
    if playerClassLevels[source] and playerClassLevels[source][classId] then
        return playerClassLevels[source][classId].level or 0
    end
    return 0
end

-- Award XP to a player's current class.  This function updates the
-- player's XP and checks if they have reached the next level.  If so,
-- their level is incremented and XP is carried over.  The updated values
-- are persisted to the database.  A small notification could be sent
-- here to inform the player, but the client-side UI for class levels is
-- beyond the scope of this implementation.
local function awardClassXP(source, amount)
    local classId = playerClasses[source]
    if not classId then return end
    local txid = getPlayerIdentifier(source)
    if not txid then return end
    if not playerClassLevels[source] then
        loadClassLevels(source)
    end
    -- Initialise record if missing
    if not playerClassLevels[source][classId] then
        playerClassLevels[source][classId] = { level = 0, xp = 0 }
    end
    local data = playerClassLevels[source][classId]
    data.xp = data.xp + (amount or 0)
    -- Define XP thresholds for each level.  These thresholds determine
    -- how much class XP is required to reach each successive level.
    -- The progression starts with small increments and gradually
    -- increases.  Levels now extend up to 22, giving players more
    -- opportunity to progress.  Each entry represents the XP required
    -- to reach that level from level 0.
    local thresholds = {
        100,   -- Level 1
        300,   -- Level 2
        600,   -- Level 3
        1000,  -- Level 4
        1500,  -- Level 5
        2100,  -- Level 6
        2800,  -- Level 7
        3600,  -- Level 8
        4500,  -- Level 9
        5500,  -- Level 10
        6600,  -- Level 11
        7800,  -- Level 12
        9100,  -- Level 13
        10500, -- Level 14
        12000, -- Level 15
        13600, -- Level 16
        15300, -- Level 17
        17100, -- Level 18
        19000, -- Level 19
        21000, -- Level 20
        23100, -- Level 21
        25300  -- Level 22
    }
    local newLevel = data.level
    -- Loop through thresholds and increase level while XP meets or exceeds
    -- the next threshold.  Carry over excess XP.
    while newLevel + 1 <= #thresholds and data.xp >= thresholds[newLevel + 1] do
        newLevel = newLevel + 1
    end
    if newLevel ~= data.level then
        data.level = newLevel
        -- Optionally notify the player of the level up here
    end
    -- Persist changes to database.  Use UPSERT semantics via ON DUPLICATE KEY.
    MySQL.Async.execute('INSERT INTO koth_player_class_levels (txid, class, level, xp) VALUES (@txid, @class, @level, @xp) ON DUPLICATE KEY UPDATE level = @level, xp = @xp', {
        ['@txid'] = txid,
        ['@class'] = classId,
        ['@level'] = data.level,
        ['@xp'] = data.xp
    }, function() end)
end

-- Event to award XP externally from other resources.  Accepts a
-- player source (server ID) and an XP amount.  Currently this is
-- triggered in the KOTH gamemode when a kill is registered.  Other
-- actions (captures, assists, etc.) could also call this event.
RegisterNetEvent('koth_classes:addClassXP')
AddEventHandler('koth_classes:addClassXP', function(targetSource, amount)
    local src = tonumber(targetSource) or source
    awardClassXP(src, tonumber(amount) or 0)
end)

--
-- Administrative override to set a player's XP for a specific class.  This event
-- can be triggered by an admin panel to directly set the XP value for the
-- target player's class.  The level will be recalculated based on the
-- configured XP thresholds.  The changes are persisted to the database and
-- cached in memory.  If the player has not yet selected the specified
-- class, a record will be created.
--
-- Parameters:
--   targetSource (number): The server ID of the player whose class XP should be set
--   classId (string): The class identifier (e.g. "assault", "medic")
--   newXP (number): The new XP value to assign
--
RegisterNetEvent('koth_classes:setClassXP')
AddEventHandler('koth_classes:setClassXP', function(targetSource, classId, newXP)
    local src = tonumber(targetSource) or source
    local xpVal = tonumber(newXP) or 0
    -- Validate class ID
    if not classId or not Config.Classes[classId] then
        return
    end
    -- Ensure the player's class level table is loaded
    if not playerClassLevels[src] then
        loadClassLevels(src)
    end
    if not playerClassLevels[src][classId] then
        playerClassLevels[src][classId] = { level = 0, xp = 0 }
    end
    local data = playerClassLevels[src][classId]
    data.xp = xpVal
    -- Recompute level based on thresholds
    local thresholds = { 100, 300, 600, 1000, 1500, 2100, 2800, 3600, 4500 }
    local newLevel = 0
    while newLevel + 1 <= #thresholds and data.xp >= thresholds[newLevel + 1] do
        newLevel = newLevel + 1
    end
    data.level = newLevel
    -- Persist to DB using upsert semantics
    local txid = getPlayerIdentifier(src)
    if txid then
        MySQL.Async.execute('INSERT INTO koth_player_class_levels (txid, class, level, xp) VALUES (@txid, @class, @level, @xp) ON DUPLICATE KEY UPDATE level = @level, xp = @xp', {
            ['@txid'] = txid,
            ['@class'] = classId,
            ['@level'] = data.level,
            ['@xp'] = data.xp
        }, function() end)
    end
    -- Optionally notify the player of their new class level via client event
    TriggerClientEvent('koth_classes:classXPUpdated', src, classId, data.level, data.xp)
end)

-- Export to get a player's class level.  This can be called from
-- other resources (e.g. the KOTH weapon shop) to determine whether a
-- weapon purchase should be allowed based on the player's class level.
exports('GetPlayerClassLevel', function(source, classId)
    return getClassLevel(source, classId)
end)

-- Player joined - don't auto-load class
AddEventHandler('playerJoining', function()
    local source = source
    local txid = getPlayerIdentifier(source)
    
    if txid then
        -- Don't auto-load class - let players choose via NPC
        debugPrint('Player', source, 'joined - no auto class assignment')
        -- Load class XP/level data into memory
        loadClassLevels(source)
    end
end)

-- Request player class
RegisterNetEvent('koth_classes:requestPlayerClass')
AddEventHandler('koth_classes:requestPlayerClass', function()
    local source = source
    local txid = getPlayerIdentifier(source)
    
    if txid then
        -- Load class levels from DB to memory so they are ready for gating and XP updates
        loadClassLevels(source)
        MySQL.Async.fetchScalar('SELECT player_class FROM koth_players WHERE txid = @txid', {
            ['@txid'] = txid
        }, function(playerClass)
            if playerClass then
                playerClasses[source] = playerClass
                TriggerClientEvent('koth_classes:setPlayerClass', source, playerClass)
                debugPrint('Sent class', playerClass, 'to player', source)
            end
        end)
    end
end)

-- Player selects a class
RegisterNetEvent('koth_classes:selectClass')
AddEventHandler('koth_classes:selectClass', function(classId)
    local source = source
    local txid = getPlayerIdentifier(source)
    
    if not classId or not Config.Classes[classId] then
        debugPrint('Invalid class ID:', classId)
        return
    end
    
    -- Check if player meets level requirement
    MySQL.Async.fetchAll('SELECT level FROM koth_players WHERE txid = @txid', {
        ['@txid'] = txid
    }, function(result)
        if result[1] then
            local playerLevel = result[1].level or 1
            local requiredLevel = Config.Classes[classId].requiredLevel or 1
            
            if playerLevel >= requiredLevel then
                -- Update database
                MySQL.Async.execute('UPDATE koth_players SET player_class = @class WHERE txid = @txid', {
                    ['@class'] = classId,
                    ['@txid'] = txid
                }, function(rowsChanged)
                    playerClasses[source] = classId
                    TriggerClientEvent('koth_classes:setPlayerClass', source, classId)
                    debugPrint('Player', source, 'selected class:', classId)
                end)
            else
                TriggerClientEvent('koth:purchaseResult', source, false, 
                    string.format('Class locked! Requires level %d (you are level %d)', requiredLevel, playerLevel))
            end
        end
    end)
end)

-- Removed server-side hotbar handling as it's handled client-side

-- Place med bag
RegisterNetEvent('koth_classes:placeMedBag')
AddEventHandler('koth_classes:placeMedBag', function(data)
    local source = source
    
    -- Generate unique ID for this med bag
    medBagIdCounter = medBagIdCounter + 1
    local medBagId = 'medbag_' .. medBagIdCounter
    
    -- Store med bag data
    activeMedBags[medBagId] = {
        source = source,
        x = data.x,
        y = data.y,
        z = data.z,
        ability = data.ability,
        endTime = GetGameTimer() + (data.ability.duration * 1000)
    }
    
    -- Notify all clients about the med bag
    TriggerClientEvent('koth_classes:medBagPlaced', -1, {
        id = medBagId,
        source = source,
        x = data.x,
        y = data.y,
        z = data.z,
        ability = data.ability
    })
    
    debugPrint('Med bag placed by', source, 'at', data.x, data.y, data.z)
    
    -- Set timer to remove med bag
    SetTimeout(data.ability.duration * 1000, function()
        if activeMedBags[medBagId] then
            activeMedBags[medBagId] = nil
            TriggerClientEvent('koth_classes:removeMedBag', -1, medBagId)
            debugPrint('Med bag', medBagId, 'expired')
        end
    end)
end)

-- Place ammo bag
RegisterNetEvent('koth_classes:placeAmmoBag')
AddEventHandler('koth_classes:placeAmmoBag', function(data)
    local source = source
    
    -- Generate unique ID for this ammo bag
    medBagIdCounter = medBagIdCounter + 1
    local ammoBagId = 'ammobag_' .. medBagIdCounter
    
    -- Store ammo bag data
    activeMedBags[ammoBagId] = {
        source = source,
        x = data.x,
        y = data.y,
        z = data.z,
        ability = data.ability,
        endTime = GetGameTimer() + (data.ability.duration * 1000)
    }
    
    -- Notify all clients about the ammo bag
    TriggerClientEvent('koth_classes:ammoBagPlaced', -1, {
        id = ammoBagId,
        source = source,
        x = data.x,
        y = data.y,
        z = data.z,
        ability = data.ability
    })
    
    debugPrint('Ammo bag placed by', source, 'at', data.x, data.y, data.z)
    
    -- Set timer to remove ammo bag
    SetTimeout(data.ability.duration * 1000, function()
        if activeMedBags[ammoBagId] then
            activeMedBags[ammoBagId] = nil
            TriggerClientEvent('koth_classes:removeAmmoBag', -1, ammoBagId)
            debugPrint('Ammo bag', ammoBagId, 'expired')
        end
    end)
end)

-- Clean up when player drops
AddEventHandler('playerDropped', function(reason)
    local source = source
    playerClasses[source] = nil
    playerClassLevels[source] = nil
    
    -- Remove any active med bags from this player
    for medBagId, medBag in pairs(activeMedBags) do
        if medBag.source == source then
            activeMedBags[medBagId] = nil
            TriggerClientEvent('koth_classes:removeMedBag', -1, medBagId)
            debugPrint('Removed med bag', medBagId, 'from dropped player', source)
        end
    end
end)

-- Export to get player class
exports('GetPlayerClass', function(source)
    return playerClasses[source]
end)

debugPrint('Server main.lua loaded')


-- Export to get a player's current XP for a specific class.  This allows
-- other resources (e.g. the KOTH gamemode) to query the player's
-- progress towards the next class level and display it in the UI.
exports('GetPlayerClassXP', function(source, classId)
    if playerClassLevels[source] and playerClassLevels[source][classId] then
        return playerClassLevels[source][classId].xp or 0
    end
    return 0
end)

-- Export to retrieve the XP threshold required to reach the next level
-- for a given current level.  The thresholds array mirrors the one
-- used in awardClassXP.  If the provided level exceeds the bounds of
-- the array, the final threshold is returned.
exports('GetClassXPThreshold', function(level)
    -- The same threshold table used in awardClassXP.  Levels now extend
    -- up to 22.  Each value represents the XP required to reach the
    -- corresponding level (1-based index).  If the requested level
    -- exceeds the defined thresholds, return the final threshold value.
    local thresholds = {
        100,   -- Level 1
        300,   -- Level 2
        600,   -- Level 3
        1000,  -- Level 4
        1500,  -- Level 5
        2100,  -- Level 6
        2800,  -- Level 7
        3600,  -- Level 8
        4500,  -- Level 9
        5500,  -- Level 10
        6600,  -- Level 11
        7800,  -- Level 12
        9100,  -- Level 13
        10500, -- Level 14
        12000, -- Level 15
        13600, -- Level 16
        15300, -- Level 17
        17100, -- Level 18
        19000, -- Level 19
        21000, -- Level 20
        23100, -- Level 21
        25300  -- Level 22
    }
    local nextIndex = (tonumber(level) or 0) + 1
    if thresholds[nextIndex] then
        return thresholds[nextIndex]
    end
    return thresholds[#thresholds]
end)
