-- Client-side class system
local currentClass = nil
local classAbilities = {}
local abilityCooldowns = {}

-- Debug print function
local function debugPrint(...)
    if Config.Debug then
        print('[KOTH Classes]', ...)
    end
end

-- Initialize class system
AddEventHandler('onClientResourceStart', function(resourceName)
    if GetCurrentResourceName() ~= resourceName then return end
    
    debugPrint('Class system initialized')
    
    -- Request player's class from server
    TriggerServerEvent('koth_classes:requestPlayerClass')
end)

-- Receive player class from server
RegisterNetEvent('koth_classes:setPlayerClass')
AddEventHandler('koth_classes:setPlayerClass', function(classId)
    debugPrint('Received class from server:', classId)
    
    if classId and Config.Classes[classId] then
        -- Store the previous class before switching
        local prevClass = currentClass

        currentClass = classId
        local classData = Config.Classes[classId]

        debugPrint('Setting up class:', classData.name)

        -- If the previous class was heavy and the new class is not,
        -- remove any armour that may have been applied by the heavy ability.
        if prevClass == 'heavy' and classId ~= 'heavy' then
            local ped = PlayerPedId()
            SetPedArmour(ped, 0)
            debugPrint('Removed armour from player due to class change from heavy to', classId)
        end

        -- Clear previous abilities
        classAbilities = {}
        abilityCooldowns = {}

        -- Clear all hotbar slots first to prevent duplicates
        if exports['hotbar'] then
            for i = 1, 5 do
                exports['hotbar']:ClearHotbarSlot(i)
            end
        end

        -- Setup class abilities
        if classData.abilities then
            for _, ability in ipairs(classData.abilities) do
                setupAbility(ability)
            end
        end

        -- Don't show notification for automatic class loading
        -- Only show when player manually selects via NPC
    end
end)

-- Setup ability in hotbar
function setupAbility(ability)
    debugPrint('Setting up ability:', ability.name, 'in slot', ability.slot)
    
    -- Add to hotbar
    if exports['hotbar'] then
        exports['hotbar']:SetHotbarItem(ability.slot, ability.name, ability.icon, 1, nil)
        debugPrint('Added', ability.name, 'to hotbar slot', ability.slot)
    else
        debugPrint('ERROR: Hotbar resource not found!')
    end
    
    -- Store ability data
    classAbilities[ability.slot] = ability
    abilityCooldowns[ability.slot] = 0
end

-- Handle hotbar item use.  In newer hotbar versions, non-weapon items trigger
-- 'hotbar:useItemClient' locally instead of only a server-side 'hotbar:useItem'.
-- Listen for both events for compatibility.
RegisterNetEvent('hotbar:useItem')
RegisterNetEvent('hotbar:useItemClient')
AddEventHandler('hotbar:useItem', function(slot, itemName)
    -- Legacy handler: forward to unified handler
    TriggerEvent('koth_classes:internalHandleHotbarUse', slot, itemName)
end)
AddEventHandler('hotbar:useItemClient', function(slot, itemName)
    -- New local handler
    TriggerEvent('koth_classes:internalHandleHotbarUse', slot, itemName)
end)

-- Unified handler for hotbar item use.  This prevents duplicating code across
-- multiple event handlers.
RegisterNetEvent('koth_classes:internalHandleHotbarUse')
AddEventHandler('koth_classes:internalHandleHotbarUse', function(slot, itemName)
    print('[KOTH Classes] Hotbar item used - slot:', slot, 'itemName:', itemName)
    print('[KOTH Classes] Current class:', currentClass)
    print('[KOTH Classes] Class abilities:', json.encode(classAbilities))
    
    -- Check if this is a class ability
    if classAbilities[slot] then
        local ability = classAbilities[slot]
        
        print('[KOTH Classes] Class ability detected:', ability.name, 'in slot', slot)
        
        -- Check cooldown
        local currentTime = GetGameTimer()
        if currentTime < abilityCooldowns[slot] then
            local remaining = math.ceil((abilityCooldowns[slot] - currentTime) / 1000)
            
            BeginTextCommandThefeedPost("STRING")
            AddTextComponentSubstringPlayerName(string.format("%s on cooldown: %d seconds", ability.name, remaining))
            EndTextCommandThefeedPostTicker(false, true)
            return
        end
        
        -- Use ability based on class
        if currentClass == 'medic' and ability.name == 'Med Bag' then
            print('[KOTH Classes] Triggering medic ability')
            TriggerEvent('koth_classes:useMedicAbility', ability)
        elseif currentClass == 'assault' and ability.name == 'Ammo Bag' then
            print('[KOTH Classes] Triggering assault ability')
            TriggerEvent('koth_classes:useAssaultAbility', ability)
        elseif currentClass == 'heavy' and ability.name == 'Armor Kit' then
            print('[KOTH Classes] Triggering heavy ability')
            TriggerEvent('koth_classes:useHeavyAbility', ability)
        elseif currentClass == 'engineer' and ability.name == 'Repair Tool' then
            print('[KOTH Classes] Triggering engineer ability')
            TriggerEvent('koth_classes:useEngineerAbility', ability)
        end
    else
        print('[KOTH Classes] No ability found in slot', slot)
    end
end)

--
-- Reset the player's class and abilities.  This event is triggered from
-- the koth_teamsel resource when a new map is loaded.  Clearing the
-- current class ensures that players must re‑select their class and
-- prevents abilities or hotbar items from persisting across rounds.
RegisterNetEvent('koth_classes:resetClass')
AddEventHandler('koth_classes:resetClass', function()
    if currentClass then
        debugPrint('Resetting class on map rotation')
        currentClass = nil
        classAbilities = {}
        abilityCooldowns = {}
        -- Clear all hotbar slots associated with class abilities
        if exports['hotbar'] then
            for i = 1, 5 do
                exports['hotbar']:ClearHotbarSlot(i)
            end
        end
    end
end)

-- Direct key detection for abilities
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        
        -- Check all number keys 1-5
        for slot = 1, 5 do
            local keyControl = 156 + slot -- 157 = 1, 158 = 2, etc.
            
            if IsControlJustPressed(0, keyControl) then
                -- Check if this slot has a class ability
                if classAbilities[slot] then
                    local ability = classAbilities[slot]
                    
                    print('[KOTH Classes] Key press detected for slot', slot, '- Ability:', ability.name)
                    
                    -- Check cooldown
                    local currentTime = GetGameTimer()
                    if currentTime < abilityCooldowns[slot] then
                        local remaining = math.ceil((abilityCooldowns[slot] - currentTime) / 1000)
                        
                        BeginTextCommandThefeedPost("STRING")
                        AddTextComponentSubstringPlayerName(string.format("%s on cooldown: %d seconds", ability.name, remaining))
                        EndTextCommandThefeedPostTicker(false, true)
                    else
                        -- Use ability based on class and slot
                        if currentClass == 'medic' and slot == 5 and ability.name == 'Med Bag' then
                            print('[KOTH Classes] Triggering medic ability from key press')
                            TriggerEvent('koth_classes:useMedicAbility', ability)
                        elseif currentClass == 'engineer' and slot == 5 and ability.name == 'Repair Tool' then
                            print('[KOTH Classes] Triggering engineer ability from key press')
                            TriggerEvent('koth_classes:useEngineerAbility', ability)
                        end
                        -- Add other class abilities here in the future
                    end
                end
            end
        end
    end
end)

-- Listen for class selection from koth_teamsel
RegisterNetEvent('koth:classSelected')
AddEventHandler('koth:classSelected', function(classId)
    debugPrint('Class selected event received:', classId)
    
    -- Send to server to save
    TriggerServerEvent('koth_classes:selectClass', classId)
end)

-- Handle weapon selection to re-add abilities after weapon purchase
RegisterNetEvent('koth:giveWeapon')
AddEventHandler('koth:giveWeapon', function(weapon, classId, price)
    debugPrint('Weapon given, re-adding class abilities')
    
    -- Wait a moment for weapon to be set
    Citizen.SetTimeout(500, function()
        -- Re-add class abilities to hotbar
        if currentClass and Config.Classes[currentClass] then
            local classData = Config.Classes[currentClass]
            if classData.abilities then
                for _, ability in ipairs(classData.abilities) do
                    setupAbility(ability)
                end
            end
        end
    end)
end)

-- Cooldown management thread
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1000) -- Check every second
        
        -- Update cooldown displays
        local currentTime = GetGameTimer()
        for slot, cooldownTime in pairs(abilityCooldowns) do
            if cooldownTime > currentTime and classAbilities[slot] then
                local remaining = math.ceil((cooldownTime - currentTime) / 1000)
                
                -- Update hotbar to show cooldown
                if exports['hotbar'] then
                    local ability = classAbilities[slot]
                    exports['hotbar']:SetHotbarItem(slot, ability.name .. " (" .. remaining .. "s)", ability.icon, 0, nil)
                end
            elseif cooldownTime > 0 and cooldownTime <= currentTime and classAbilities[slot] then
                -- Cooldown finished, restore normal display
                local ability = classAbilities[slot]
                exports['hotbar']:SetHotbarItem(slot, ability.name, ability.icon, 1, nil)
                abilityCooldowns[slot] = 0
            end
        end
    end
end)

-- Ensure class abilities remain in the hotbar, even if other resources (like the weapon scanner)
-- overwrite slot 5.  This watcher runs periodically and re-adds the ability for the engineer
-- class if it is missing from the hotbar or classAbilities.  Without this, the frequent
-- weapon scanning in the hotbar resource can push out the engineer ability and leave the
-- slot empty.  We intentionally check slot 5 because all class abilities use this slot.
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(2000)
        -- Only apply for the engineer class
        if currentClass == 'engineer' and Config.Classes.engineer and Config.Classes.engineer.abilities then
            local ability = Config.Classes.engineer.abilities[1]
            -- Ensure ability exists in our local tracking table
            if not classAbilities[ability.slot] then
                debugPrint('Engineer ability missing from classAbilities, re-adding')
                setupAbility(ability)
            else
                -- Check the hotbar itself via export to make sure the icon hasn't been replaced
                local hotbarState = nil
                if exports['hotbar'] and exports['hotbar'].GetHotbarItems then
                    hotbarState = exports['hotbar']:GetHotbarItems()
                end
                if hotbarState and hotbarState[ability.slot] and hotbarState[ability.slot].weaponHash ~= nil then
                    -- Slot is occupied by a weapon, restore our ability
                    debugPrint('Engineer ability overwritten by weapon in slot', ability.slot, '- restoring')
                    setupAbility(ability)
                end
            end
        end
    end
end)

-- Export functions
exports('GetCurrentClass', function()
    return currentClass
end)

exports('SetAbilityCooldown', function(slot, duration)
    abilityCooldowns[slot] = GetGameTimer() + (duration * 1000)
end)

debugPrint('Client main.lua loaded')

--[[
  Debug command to manually set up the medic class.  Disabled for production.
]]
-- RegisterCommand('testmedic', function()
    print('[KOTH Classes] Testing medic class setup')
    
    -- Force set to medic class
    currentClass = 'medic'
    local classData = Config.Classes['medic']
    
    -- Clear and setup abilities
    classAbilities = {}
    abilityCooldowns = {}
    
    -- Clear all hotbar slots first to prevent duplicates
    if exports['hotbar'] then
        for i = 1, 5 do
            exports['hotbar']:ClearHotbarSlot(i)
        end
        print('[KOTH Classes] Cleared all hotbar slots')
    end
    
    if classData.abilities then
        for _, ability in ipairs(classData.abilities) do
            setupAbility(ability)
        end
    end
    
    print('[KOTH Classes] Medic class set up manually')
    print('[KOTH Classes] Current class:', currentClass)
    print('[KOTH Classes] Abilities:', json.encode(classAbilities))
-- end, false)

--[[
  Debug command to directly trigger the medic ability (med bag).  Disabled for production.
]]
-- RegisterCommand('testmedbag', function()
    print('[KOTH Classes] Testing med bag placement directly')
    
    if Config.Classes.medic and Config.Classes.medic.abilities[1] then
        local ability = Config.Classes.medic.abilities[1]
        print('[KOTH Classes] Triggering med bag ability:', json.encode(ability))
        TriggerEvent('koth_classes:useMedicAbility', ability)
    else
        print('[KOTH Classes] Med bag ability not found in config')
    end
-- end, false)

--[[
  Debug command to print the current class state.  Disabled for production.
]]
-- RegisterCommand('checkclass', function()
    print('[KOTH Classes] Current class:', currentClass)
    print('[KOTH Classes] Class abilities:', json.encode(classAbilities))
    print('[KOTH Classes] Cooldowns:', json.encode(abilityCooldowns))
-- end, false)

--[[
  Fallback command to trigger the medic ability.  Disabled for production as
  the ability should be bound to a key or UI.
]]
-- RegisterCommand('medic', function()
    print('[KOTH Classes] /medic command executed')
    
    if currentClass == 'medic' then
        print('[KOTH Classes] Player is medic, executing testmedbag')
        ExecuteCommand('testmedbag')
    else
        print('[KOTH Classes] Player is not medic class, current class:', currentClass)
        BeginTextCommandThefeedPost("STRING")
        AddTextComponentSubstringPlayerName("You must be a medic to use this ability!")
        EndTextCommandThefeedPostTicker(false, true)
    end
-- end, false)

--[[
  Debug command to manually set up the assault class.  Disabled for production.
]]
-- RegisterCommand('testassault', function()
    print('[KOTH Classes] Testing assault class setup')
    
    -- Force set to assault class
    currentClass = 'assault'
    local classData = Config.Classes['assault']
    
    -- Clear and setup abilities
    classAbilities = {}
    abilityCooldowns = {}
    
    -- Clear all hotbar slots first to prevent duplicates
    if exports['hotbar'] then
        for i = 1, 5 do
            exports['hotbar']:ClearHotbarSlot(i)
        end
        print('[KOTH Classes] Cleared all hotbar slots')
    end
    
    if classData.abilities then
        for _, ability in ipairs(classData.abilities) do
            setupAbility(ability)
        end
    end
    
    print('[KOTH Classes] Assault class set up manually')
    print('[KOTH Classes] Current class:', currentClass)
    print('[KOTH Classes] Abilities:', json.encode(classAbilities))
-- end, false)

--[[
  Debug command to test the assault ammo bag ability.  Disabled for production.
]]
-- RegisterCommand('testammobag', function()
    print('[KOTH Classes] Testing ammo bag placement directly')
    
    if Config.Classes.assault and Config.Classes.assault.abilities[1] then
        local ability = Config.Classes.assault.abilities[1]
        print('[KOTH Classes] Triggering ammo bag ability:', json.encode(ability))
        TriggerEvent('koth_classes:useAssaultAbility', ability)
    else
        print('[KOTH Classes] Ammo bag ability not found in config')
    end
-- end, false)

--[[
  Fallback command to trigger the assault ability.  Disabled for production.
]]
-- RegisterCommand('assault', function()
    print('[KOTH Classes] /assault command executed')
    
    if currentClass == 'assault' then
        print('[KOTH Classes] Player is assault, executing testammobag')
        ExecuteCommand('testammobag')
    else
        print('[KOTH Classes] Player is not assault class, current class:', currentClass)
        BeginTextCommandThefeedPost("STRING")
        AddTextComponentSubstringPlayerName("You must be an assault to use this ability!")
        EndTextCommandThefeedPostTicker(false, true)
    end
-- end, false)

--[[
  Debug command to manually set up the heavy class.  Disabled for production.
]]
-- RegisterCommand('testheavy', function()
    print('[KOTH Classes] Testing heavy class setup')
    
    -- Force set to heavy class
    currentClass = 'heavy'
    local classData = Config.Classes['heavy']
    
    -- Clear and setup abilities
    classAbilities = {}
    abilityCooldowns = {}
    
    -- Clear all hotbar slots first to prevent duplicates
    if exports['hotbar'] then
        for i = 1, 5 do
            exports['hotbar']:ClearHotbarSlot(i)
        end
        print('[KOTH Classes] Cleared all hotbar slots')
    end
    
    if classData.abilities then
        for _, ability in ipairs(classData.abilities) do
            setupAbility(ability)
        end
    end
    
    print('[KOTH Classes] Heavy class set up manually')
    print('[KOTH Classes] Current class:', currentClass)
    print('[KOTH Classes] Abilities:', json.encode(classAbilities))
-- end, false)

--[[
  Debug command to test the heavy armor ability.  Disabled for production.
]]
-- RegisterCommand('testarmor', function()
    print('[KOTH Classes] Testing armor kit directly')
    
    if Config.Classes.heavy and Config.Classes.heavy.abilities[1] then
        local ability = Config.Classes.heavy.abilities[1]
        print('[KOTH Classes] Triggering armor kit ability:', json.encode(ability))
        TriggerEvent('koth_classes:useHeavyAbility', ability)
    else
        print('[KOTH Classes] Armor kit ability not found in config')
    end
-- end, false)

--[[
  Fallback command to trigger the heavy ability.  Disabled for production.
]]
-- RegisterCommand('heavy', function()
    print('[KOTH Classes] /heavy command executed')
    
    if currentClass == 'heavy' then
        print('[KOTH Classes] Player is heavy, executing testarmor')
        ExecuteCommand('testarmor')
    else
        print('[KOTH Classes] Player is not heavy class, current class:', currentClass)
        BeginTextCommandThefeedPost("STRING")
        AddTextComponentSubstringPlayerName("You must be a heavy to use this ability!")
        EndTextCommandThefeedPostTicker(false, true)
    end
-- end, false)
