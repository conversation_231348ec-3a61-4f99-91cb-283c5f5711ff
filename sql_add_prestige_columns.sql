--
-- SQL migration to add prestige columns to the koth_players table.
--
-- This migration introduces three new columns to persist the
-- prestige system state for each player:
--   prestige_rank           INT DEFAULT 0
--   prestige_weapon_tokens  INT DEFAULT 0
--   prestige_vehicle_tokens INT DEFAULT 0
--
-- The prestige_rank column tracks how many times a player has
-- prestiged (reset their level in exchange for tokens).  The tokens
-- columns store the number of unspent prestige tokens the player
-- possesses.  When a player prestiges, they earn one weapon token
-- and one vehicle token.  Tokens can then be spent to permanently
-- unlock VIP weapons or a prestige vehicle.

ALTER TABLE koth_players
  ADD COLUMN IF NOT EXISTS prestige_rank INT NOT NULL DEFAULT 0,
  ADD COLUMN IF NOT EXISTS prestige_weapon_tokens INT NOT NULL DEFAULT 0,
  ADD COLUMN IF NOT EXISTS prestige_vehicle_tokens INT NOT NULL DEFAULT 0;