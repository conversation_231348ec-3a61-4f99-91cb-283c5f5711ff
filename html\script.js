console.log('[KOTH] Script loading...');

window.addEventListener('message', function(ev) {
  console.log('[KOTH] Received message:', ev.data);

  try {
    if (!ev.data || !ev.data.action) return;

    // Handle permanent HUD events first (don't need overlay)
    if (ev.data.action === 'updateMoney') {
      updatePlayerMoney(ev.data.money);
      return;
    } else if (ev.data.action === 'updateTeamCounts') {
      console.log('[KOTH] Received updateTeamCounts message:', ev.data);
      updateTeamCountsData(ev.data.counts);
      return;
    } else if (ev.data.action === 'updateZonePoints') {
      console.log('[KOTH] Updating zone points:', ev.data.points);
      updateZonePointsData(ev.data.points);
      return;
    } else if (ev.data.action === 'updateKothZone') {
      console.log('[KOTH] Update zone status', ev.data.zoneData);
      if (ev.data.zoneData) {
        currentGameData.kothStatus = {
          active: true,
          controllingTeam: ev.data.zoneData.controlling,
          captureProgress: ev.data.zoneData.progress || 0,
          captureThreshold: ev.data.zoneData.threshold || 100,
          isContested: ev.data.zoneData.contested || false,
          dominantTeam: ev.data.zoneData.dominant
        };
        showKothZoneStatus();
        updateKothZoneStatus();
      }
      return;
    } else if (ev.data.action === 'hideKothZone') {
      console.log('[KOTH] Hide zone status');
      hideKothZoneStatus();
      return;
    } else if (ev.data.action === 'updateKothStatus') {
      console.log('[KOTH] KOTH status update received:', ev.data.status);
      updateKothStatus(ev.data.status);
      return;
    } else if (ev.data.action === 'initHUD') {
      console.log('[KOTH] Initialize HUD with data:', ev.data);
      if (ev.data.playerData) {
        currentGameData = { ...currentGameData, ...ev.data.playerData };
      }
      initializeGameHUD();
      return;
    } else if (ev.data.action === 'initializeHUD') {
      console.log('[KOTH] Initialize HUD with player data:', ev.data.playerData);
      // Set initial player data if provided
      if (ev.data.playerData) {
        // Update the current game data with provided values
        if (ev.data.playerData.money !== undefined) currentGameData.playerMoney = ev.data.playerData.money;
        if (ev.data.playerData.xp !== undefined) currentGameData.playerXP = ev.data.playerData.xp;
        if (ev.data.playerData.level !== undefined) currentGameData.playerLevel = ev.data.playerData.level;
        if (ev.data.playerData.kills !== undefined) currentGameData.playerKills = ev.data.playerData.kills;
        if (ev.data.playerData.player_name !== undefined) currentGameData.playerName = ev.data.playerData.player_name;
        
        // Update the display immediately
        updatePlayerInfo();
      }
      initializeGameHUD();
      return;
    } else if (ev.data.action === 'showDeathScreen') {
      console.log('[KOTH] Show death screen:', ev.data.killer);
      showDeathScreen(ev.data.killer);
      return;
    } else if (ev.data.action === 'hideDeathScreen') {
      console.log('[KOTH] Hide death screen');
      hideDeathScreen();
      return;
    } else if (ev.data.action === 'showKillReward') {
      console.log('[KOTH] Show kill reward event received:', ev.data);
      showKillReward(ev.data);
      return;
    } else if (ev.data.action === 'showLevelUp') {
      console.log('[KOTH] Show level up:', ev.data);
      showLevelUp(ev.data);
      return;
    } else if (ev.data.action === 'updatePlayerData') {
      console.log('[KOTH] Update player data received:', ev.data);
      console.log('[KOTH] Data object:', ev.data.data);
      updatePlayerData(ev.data.data);
      return;
    } else if (ev.data.action === 'killFeed') {
      // Display a kill feed message.  Pass the payload directly to
      // the helper which handles rendering and fadeout.  The
      // payload should contain killerName, victimName, killerTeam and
      // victimTeam fields.
      showKillFeedMessage(ev.data);
      return;

    } else if (ev.data.action === 'openPrestigeMenu') {
      // Open the prestige shop overlay with the provided data
      console.log('[KOTH] Open prestige menu:', ev.data.data);
      openPrestigeMenu(ev.data.data || {});
      return;
    } else if (ev.data.action === 'closePrestigeMenu') {
      // Hide the prestige shop overlay
      console.log('[KOTH] Close prestige menu');
      closePrestigeMenu();
      return;

    } else if (ev.data.action === 'showMapVote') {
      // Display the clickable map vote panel.  The payload contains
      // `options` (array of {index, name}) and `duration` (ms).  Create
      // buttons for each option that send a vote when clicked.  Also
      // start a countdown timer.
      const panel = document.getElementById('map-vote-panel');
      const optionsContainer = document.getElementById('map-vote-options');
      const timerLabel = document.getElementById('map-vote-timer');
      if (panel && optionsContainer && timerLabel) {
        optionsContainer.innerHTML = '';
        // Clear any existing interval
        if (window.mapVoteInterval) {
          clearInterval(window.mapVoteInterval);
          window.mapVoteInterval = null;
        }
        const duration = ev.data.duration || 30000;
        const startTime = Date.now();
        // Create buttons for each map option.  Each option has a
        // data-index attribute and contains a name span and a vote
        // count badge so that vote totals can be shown live via
        // updateVoteCounts messages.
        (ev.data.options || []).forEach(function(opt) {
          const btn = document.createElement('div');
          btn.className = 'map-vote-option';
          // Store index for later reference (string to ensure object keys)
          btn.dataset.index = String(opt.index);
          // Create a span for the map name
          const nameSpan = document.createElement('span');
          nameSpan.className = 'map-name';
          nameSpan.textContent = opt.name;
          // Create a span for the vote count (initially zero)
          const countSpan = document.createElement('span');
          countSpan.className = 'vote-count';
          countSpan.textContent = '0';
          // Append spans to the button
          btn.appendChild(nameSpan);
          btn.appendChild(countSpan);
          btn.addEventListener('click', function() {
            // Highlight selection
            document.querySelectorAll('.map-vote-option').forEach(function(o) {
              o.classList.remove('selected');
            });
            btn.classList.add('selected');
            // Send the vote to the client (then server) via the NUI callback
            fetch(`https://${GetParentResourceName()}/voteMap`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json; charset=UTF-8'
              },
              body: JSON.stringify({ index: opt.index })
            }).catch(function(err) {
              console.error('[KOTH] voteMap fetch error:', err);
            });
          });
          optionsContainer.appendChild(btn);
        });
        // Start countdown update
        timerLabel.textContent = 'Time left: ' + Math.floor(duration / 1000) + 's';
        window.mapVoteInterval = setInterval(function() {
          const elapsed = Date.now() - startTime;
          const remaining = Math.max(0, duration - elapsed);
          timerLabel.textContent = 'Time left: ' + Math.ceil(remaining / 1000) + 's';
          if (remaining <= 0) {
            clearInterval(window.mapVoteInterval);
            window.mapVoteInterval = null;
          }
        }, 500);
        // Show panel and ensure the overlay is visible.  The map vote panel
        // lives inside the overlay, so we must display the overlay or
        // the panel will remain hidden.
        panel.style.display = 'flex';
        const ov = document.getElementById('overlay');
        if (ov) {
          ov.style.display = 'flex';
        }
      }
      return;

    } else if (ev.data.action === 'hideMapVote') {
      // Hide the vote panel and clear timer
      const panel = document.getElementById('map-vote-panel');
      if (panel) {
        panel.style.display = 'none';
      }
      // Hide the overlay as well if nothing else is using it
      const ov = document.getElementById('overlay');
      if (ov) {
        ov.style.display = 'none';
      }
      if (window.mapVoteInterval) {
        clearInterval(window.mapVoteInterval);
        window.mapVoteInterval = null;
      }
      return;
    } else if (ev.data.action === 'updateVoteCounts') {
      // When the server broadcasts updated vote counts, update the
      // corresponding badges in the map vote panel.  The payload
      // includes a `counts` object keyed by map index.  Each map
      // option created in the showMapVote handler stores its index
      // in data-index; we use that to find the correct count.  If a
      // button lacks a vote count span, create one on the fly.
      const counts = ev.data.counts || {};
      document.querySelectorAll('.map-vote-option').forEach(function(el) {
        const idxStr = el.dataset.index;
        if (!idxStr) return;
        // Parse the index as an integer because the counts object
        // provided by the server uses numeric keys.  If parsing
        // fails, fallback to string lookup.
        const idxNum = parseInt(idxStr, 10);
        let count = 0;
        if (!Number.isNaN(idxNum) && counts[idxNum] !== undefined) {
          count = counts[idxNum] || 0;
        } else if (counts[idxStr] !== undefined) {
          count = counts[idxStr] || 0;
        }
        let badge = el.querySelector('.vote-count');
        if (!badge) {
          badge = document.createElement('span');
          badge.className = 'vote-count';
          el.appendChild(badge);
        }
        badge.textContent = count;
      });
      return;
    }

    const overlay = document.getElementById('overlay');
    const teamSelect = document.getElementById('team-select');
    const menuContainer = document.getElementById('menu-container');
    const menuTitle = document.getElementById('menu-title');
    const itemsContainer = document.getElementById('items');

    if (!overlay) {
      console.error('[KOTH] Missing overlay element');
      return;
    }

    if (ev.data.action === 'showTeamSelect') {
      console.log('[KOTH] Showing team select');

      if (teamSelect) {
        // Update counts if provided
        if (ev.data.counts) {
          for (let team in ev.data.counts) {
            const countEl = document.getElementById('count-' + team);
            if (countEl) {
              countEl.textContent = ev.data.counts[team];
            }
          }
          
          // Hide loading text once data is loaded
          const loadingText = document.querySelector('.loading-text');
          if (loadingText) {
            loadingText.style.display = 'none';
            console.log('[KOTH] Loading text hidden');
          }
          
          // Also hide loading indicator if it exists
          const loadingIndicator = document.querySelector('.loading-indicator');
          if (loadingIndicator) {
            loadingIndicator.style.display = 'none';
            console.log('[KOTH] Loading indicator hidden');
          }
        }

        overlay.style.display = 'flex';
        teamSelect.style.display = 'flex';
        if (menuContainer) menuContainer.style.display = 'none';
      }

    } else if (ev.data.action === 'showMenu') {
      console.log('[KOTH] Showing menu:', ev.data.type);

      if (menuContainer && menuTitle && itemsContainer) {
        if (teamSelect) teamSelect.style.display = 'none';

        menuTitle.textContent = ev.data.type === 'vehicles' ? 'Vehicles' : 'Classes';
        itemsContainer.innerHTML = '';

        if (ev.data.type === 'vehicles' && ev.data.items) {
          // Show vehicles shop instead of old menu
          const vehiclesShop = document.getElementById('vehicles-shop');
          const vehiclesGrid = document.getElementById('vehicles-grid');
          const vehiclePlayerMoney = document.getElementById('vehicle-player-money');

          if (vehiclesShop && vehiclesGrid) {
            // Hide other UIs
            if (teamSelect) teamSelect.style.display = 'none';
            if (menuContainer) menuContainer.style.display = 'none';

            // Update player money - handle both number and undefined cases
            console.log('[KOTH] Vehicle shop money data:', ev.data.money);
            const moneyValue = typeof ev.data.money === 'number' ? ev.data.money : 0;
            if (vehiclePlayerMoney) {
              vehiclePlayerMoney.textContent = moneyValue.toLocaleString();
              console.log('[KOTH] Updated vehicle shop money display to:', moneyValue);
              
              // Flash money display to show it updated
              vehiclePlayerMoney.style.color = '#00ff00';
              setTimeout(() => {
                vehiclePlayerMoney.style.color = '';
              }, 500);
            }

            // Clear and populate vehicles grid
            vehiclesGrid.innerHTML = '';


            // Determine VIP status from the payload.  This flag is set by
            // the server based on the player's Discord role.  A true
            // value means the player can access VIP‑only vehicles.  If
            // false, these vehicles will display "VIP ONLY" and remain
            // locked.  A separate `prestigeOnly` flag is used on items
            // that require prestige tokens to unlock.
            const isVip = !!ev.data.isVip;
            const playerLevel = ev.data.playerLevel || 1;
            ev.data.items.forEach(function(v) {
              // Determine ownership for gating.  Prestige vehicles
              // remain locked until the player has purchased them via
              // the prestige shop.  VIP vehicles require isVip to be true.
              const isOwned = v.owned === true;
              const lockedVip = (v.vipOnly === true) && !isVip && !isOwned;
              const lockedPrestige = (v.prestigeOnly === true) && !isOwned;
              const lockedLevel = (v.requiredLevel && playerLevel < v.requiredLevel) && !isOwned;
              const locked = lockedVip || lockedLevel || lockedPrestige;

              const card = document.createElement('div');
              card.className = 'vehicle-card';

              // Default price strings.  Vehicles with a cost of 0 are shown as FREE
              // instead of $0 to make it clear they are intentionally free.
              let priceStr = (v.cost && v.cost > 0) ? '$' + v.cost.toLocaleString() : 'FREE';
              let rentStr = '';
              if (v.rent !== undefined) {
                rentStr = (v.rent > 0) ? 'Rent: $' + v.rent.toLocaleString() : 'Rent: $0';
              }
              let buttonsHtml = '';

              if (isOwned) {
                // Owned vehicles: show owned status and spawn button
                priceStr = 'OWNED';
                rentStr = '';
                buttonsHtml = '<div class="vehicle-buttons"><button class="vehicle-spawn-btn" data-name="' + v.name + '" data-price="0" data-type="spawn">SPAWN</button></div>';
              } else if (locked) {
                // When locked, display appropriate message based on
                // which lock applies.  Prestige items show PRESTIGE
                // ONLY, VIP items show VIP ONLY, and level gated
                // items show the level requirement.
                if (lockedPrestige) {
                  priceStr = 'PRESTIGE ONLY';
                  rentStr = '';
                } else if (lockedVip) {
                  priceStr = 'VIP ONLY';
                  rentStr = '';
                } else if (lockedLevel) {
                  priceStr = 'LVL ' + v.requiredLevel;
                  rentStr = 'Unlock at level ' + v.requiredLevel;
                }
                // No buy/rent buttons for locked vehicles
                buttonsHtml = '<div class="vehicle-buttons"><button class="vehicle-buy-btn disabled" disabled>LOCKED</button></div>';
              } else {
                // Check affordability
                const playerMoneyAmount = moneyValue;
                const canAffordBuy = playerMoneyAmount >= v.cost;
                const canAffordRent = playerMoneyAmount >= v.rent;
                console.log(`[KOTH] Vehicle ${v.name}: buy=$${v.cost}, rent=$${v.rent}, playerMoney=$${playerMoneyAmount}, canAffordBuy=${canAffordBuy}, canAffordRent=${canAffordRent}`);
                if (!canAffordBuy && !canAffordRent) {
                  card.className += ' unaffordable';
                }
                buttonsHtml =
                  '<div class="vehicle-buttons">' +
                    '<button class="vehicle-buy-btn' + (canAffordBuy ? '' : ' disabled') + '" data-name="' + v.name + '" data-price="' + v.cost + '" data-type="buy" ' + (canAffordBuy ? '' : 'disabled') + '>BUY</button>' +
                    '<button class="vehicle-rent-btn' + (canAffordRent ? '' : ' disabled') + '" data-name="' + v.name + '" data-price="' + v.rent + '" data-type="rent" ' + (canAffordRent ? '' : 'disabled') + '>RENT</button>' +
                  '</div>';
              }

              card.innerHTML =
                '<img src="' + v.img + '" alt="' + v.name + '" class="vehicle-image">' +
                '<div class="vehicle-name">' + v.name + '</div>' +
                '<div class="vehicle-price">' + priceStr + '</div>' +
                (rentStr ? '<div class="vehicle-rent-price">' + rentStr + '</div>' : '') +
                buttonsHtml;
              vehiclesGrid.appendChild(card);
            });

            overlay.style.display = 'flex';
            vehiclesShop.style.display = 'block';
            return; // Skip the old menu logic
          }
        } else if (ev.data.type === 'classes' && ev.data.items) {
          // Show classes selection instead of old menu
          const classesSelection = document.getElementById('classes-selection');
          const classesContainer = document.querySelector('.classes-grid'); // Changed to match HTML

          if (classesSelection && classesContainer) {
            // Hide other UIs
            if (teamSelect) teamSelect.style.display = 'none';
            if (menuContainer) menuContainer.style.display = 'none';

            // Clear and populate classes container
            classesContainer.innerHTML = '';
            
            // Store player money for later use in weapon shop
            const playerMoney = typeof ev.data.money === 'number' ? ev.data.money : 0;
            window.lastKnownPlayerMoney = playerMoney;
            
            // Update money display in classes header
            const classesMoneyElement = document.getElementById('classes-player-money');
            if (classesMoneyElement) {
              classesMoneyElement.textContent = playerMoney.toLocaleString();
            }

            ev.data.items.forEach(function(c) {
              const card = document.createElement('div');
              card.className = 'class-card';
              if (c.id === 'medic') card.className += ' medic';
              if (c.locked) card.className += ' locked';

              const lockIcon = c.locked ? '<div class="class-lock-icon">🔒</div>' : '';

              card.innerHTML =
                '<img src="' + c.img + '" alt="' + (c.name || c.label) + '" class="class-image">' +
                '<div class="class-info">' +
                  '<div class="class-name' + (c.id === 'medic' ? ' medic' : '') + '">' + (c.name || c.label) + '</div>' +
                  '<div class="class-unlock">' + (c.unlock || 'Unlock at level 1') + '</div>' +
                  lockIcon +
                '</div>';

              card.setAttribute('data-id', c.id);
              card.setAttribute('data-type', 'class');
              card.setAttribute('data-locked', c.locked ? 'true' : 'false');
              classesContainer.appendChild(card);
            });

            overlay.style.display = 'flex';
            classesSelection.style.display = 'block';
            return; // Skip the old menu logic
          }
        }

        overlay.style.display = 'flex';
        menuContainer.style.display = 'flex';
      }

    } else if (ev.data.action === 'showWeaponSelect') {
      console.log('[KOTH] Showing weapon selection for class:', ev.data.class);

      const weaponsShop = document.getElementById('weapons-shop');
      const weaponsGrid = document.getElementById('weapons-grid');
      const playerMoney = document.getElementById('player-money');
      const classesSelection = document.getElementById('classes-selection');

      if (weaponsShop && weaponsGrid) {
        // Hide other UIs including classes selection
        if (teamSelect) {
          teamSelect.style.display = 'none';
          console.log('[KOTH] Hidden team select');
        }
        if (menuContainer) {
          menuContainer.style.display = 'none';
          console.log('[KOTH] Hidden menu container');
        }
        if (classesSelection) {
          classesSelection.style.display = 'none';
          console.log('[KOTH] Hidden classes selection');
        }

        // Update player money - handle both number and undefined cases
        console.log('[KOTH] Weapon shop money data:', ev.data.money);
        const moneyValue = typeof ev.data.money === 'number' ? ev.data.money : (window.lastKnownPlayerMoney || 0);
        if (playerMoney) {
          playerMoney.textContent = moneyValue.toLocaleString();
          console.log('[KOTH] Updated weapon shop money display to:', moneyValue);
          
          // Flash money display to show it updated
          playerMoney.style.color = '#00ff00';
          setTimeout(() => {
            playerMoney.style.color = '';
          }, 500);
        }

            // Update class level display in the weapon shop header if present
        const classLevelEl = document.getElementById('weapon-shop-level');
        if (classLevelEl) {
          classLevelEl.textContent = (ev.data.classLevel || 0);
        }
        // Update class XP progress bar in the weapon shop.  Use the
        // values sent from the server; hide the bar if values are
        // missing or invalid.
        const shopXpFill = document.getElementById('shop-class-xp-fill');
        const shopXpText = document.getElementById('shop-class-xp-text');
        if (shopXpFill && shopXpText && typeof ev.data.classXP === 'number' && typeof ev.data.classMaxXP === 'number' && ev.data.classMaxXP > 0) {
          // Calculate progress relative to the current class level.  The
          // server provides classXP as a cumulative total and classMaxXP
          // as the cumulative threshold for the next level.  We need the
          // previous threshold to compute per‑level progress.  Mirror
          // the thresholds used on the server (koth_classes resource).
          // Mirror the XP thresholds defined on the server for class levels.  These
          // values represent the cumulative XP required to reach each level
          // (starting from level 1).  Extending this list allows class levels
          // to progress up to level 22.
          const classThresholds = [
            100,   // Level 1
            300,   // Level 2
            600,   // Level 3
            1000,  // Level 4
            1500,  // Level 5
            2100,  // Level 6
            2800,  // Level 7
            3600,  // Level 8
            4500,  // Level 9
            5500,  // Level 10
            6600,  // Level 11
            7800,  // Level 12
            9100,  // Level 13
            10500, // Level 14
            12000, // Level 15
            13600, // Level 16
            15300, // Level 17
            17100, // Level 18
            19000, // Level 19
            21000, // Level 20
            23100, // Level 21
            25300  // Level 22
          ];
          const lvl = typeof ev.data.classLevel === 'number' ? ev.data.classLevel : 0;
          // Determine previous cumulative threshold.  Levels are 0‑indexed for
          // the array; for level 0 (no class), prev is 0.  Clamp index to
          // bounds of the array.
          let prevThreshold = 0;
          if (lvl > 0) {
            const idx = Math.min(lvl - 1, classThresholds.length - 1);
            prevThreshold = classThresholds[idx] || 0;
          }
          const nextThreshold = ev.data.classMaxXP;
          const currentXP = ev.data.classXP;
          const progressXP = Math.max(0, currentXP - prevThreshold);
          const neededXP = Math.max(1, nextThreshold - prevThreshold);
          const perc = Math.min((progressXP / neededXP) * 100, 100);
          shopXpFill.style.width = `${perc}%`;
          shopXpText.textContent = `${progressXP} / ${neededXP} XP`;
          shopXpText.parentElement.style.display = '';
        } else if (shopXpFill && shopXpText) {
          shopXpFill.style.width = '0%';
          shopXpText.textContent = '';
          if (shopXpText.parentElement) {
            shopXpText.parentElement.style.display = 'none';
          }
        }

        // Clear and populate weapons grid
        weaponsGrid.innerHTML = '';

        if (ev.data.weapons) {
          // Capture the player's class level from the payload.  This will
          // be used to determine if weapons are locked behind class
          // progression.  Default to 0 when not provided to maximise
          // gating.
          const classLevel = ev.data.classLevel || 0;

          // Separate VIP and normal weapons so we can render a locked VIP section.
          // Treat the VIP flag as the Prestige flag.  Prestige weapons are
          // always visible to players, regardless of a VIP role.  We
          // therefore set isVip to true to avoid gating the Prestige
          // category behind VIP status.
          const isVip = true;
          const normalWeapons = [];
          const vipWeapons = [];
          ev.data.weapons.forEach(function(w) {
            if (w.vipOnly) {
              vipWeapons.push(w);
            } else {
              normalWeapons.push(w);
            }
          });

          /*
           * Helper to create a weapon card.  Accepts a weapon object and a flag
           * indicating whether the card should be locked due to VIP status
           * (lockedVip).  The returned DOM element reflects the following logic:
           *   • If the item is VIP locked or level locked (based on
           *     w.requiredLevel and the player's classLevel), the card
           *     displays a lock overlay with either "VIP ONLY" or the
           *     required level (e.g. "LVL 3").  No action buttons are added.
           *   • If the item is owned by the player (w.owned === true) or has
           *     a price of 0, the card shows "FREE" and a single spawn
           *     button.  Clicking this button will send a purchaseType of
           *     "buy" with a price of 0 so the server treats it as a
           *     free spawn.
           *   • Otherwise, the card displays the purchase price and a 30%
           *     rental price, along with BUY and RENT buttons.  These
           *     buttons are disabled when the player cannot afford the
           *     corresponding cost.
           */
          function createWeaponCard(w, lockedVip) {
            const card = document.createElement('div');
            card.className = 'weapon-card';

            // Extract price information; treat undefined or falsy values as 0
            const price = Number(w.price) || 0;
            const rentPrice = price > 0 ? Math.floor(price * 0.3) : 0;
            const owned = w.owned === true;

            // Determine level lock: if a requiredLevel exists and the
            // player's classLevel is below it and the item is not owned,
            // mark it as level locked.  Missing requiredLevel or owned
            // items bypass the level lock.
            let levelLocked = false;
            if (w.requiredLevel !== undefined && !owned) {
              const req = parseInt(w.requiredLevel, 10);
              if (!isNaN(req) && classLevel < req) {
                levelLocked = true;
              }
            }

            // Compute final locked state combining VIP and level locks
            const finalLocked = lockedVip || levelLocked;

            // Determine affordability for purchase and rent actions
            const playerMoneyAmount = moneyValue;
            const canAffordBuy = playerMoneyAmount >= price;
            const canAffordRent = playerMoneyAmount >= rentPrice;

            // Build card inner HTML
            let inner = '';
            // Image and name
            // Use a robust fallback image when the weapon image is missing.  The
            // default fallback is the unarmed icon located under images/guns.
            inner += '<img src="' + (w.img || 'images/guns/unarmed.png') + '" alt="' + (w.name || w.weapon) + '" class="weapon-image">';
            inner += '<div class="weapon-name">' + (w.name || w.weapon) + '</div>';

            if (finalLocked) {
              // Locked items: show lock reason.  For prestige (VIP) locked
              // items, display 'PRESTIGE ONLY' instead of 'VIP ONLY'.
              if (lockedVip) {
                inner += '<div class="weapon-price vip-only">PRESTIGE ONLY</div>';
              } else {
                const lvlText = w.requiredLevel !== undefined ? 'LVL ' + w.requiredLevel : 'LOCKED';
                inner += '<div class="weapon-price level-locked">' + lvlText + '</div>';
              }
            } else if (owned || price === 0) {
              // Owned or free items: show FREE and spawn button
              inner += '<div class="weapon-price">FREE</div>';
              inner += '<div class="weapon-buttons">';
              inner += '<button class="weapon-buy-btn" data-weapon="' + w.weapon + '" data-class="' + ev.data.class + '" data-price="0" data-type="buy">SPAWN</button>';
              inner += '</div>';
            } else {
              // Purchasable/rentable items
              inner += '<div class="weapon-price">$' + price.toLocaleString() + '</div>';
              inner += '<div class="weapon-rent-price">Rent: $' + rentPrice.toLocaleString() + '</div>';
              inner += '<div class="weapon-buttons">';
              inner += '<button class="weapon-buy-btn' + (canAffordBuy ? '' : ' disabled') + '" data-weapon="' + w.weapon + '" data-class="' + ev.data.class + '" data-price="' + price + '" data-type="buy" ' + (canAffordBuy ? '' : 'disabled') + '>BUY</button>';
              inner += '<button class="weapon-rent-btn' + (canAffordRent ? '' : ' disabled') + '" data-weapon="' + w.weapon + '" data-class="' + ev.data.class + '" data-price="' + rentPrice + '" data-type="rent" ' + (canAffordRent ? '' : 'disabled') + '>RENT</button>';
              inner += '</div>';
            }

            card.innerHTML = inner;

            // Apply unaffordable class when not locked and cannot afford either option
            if (!finalLocked && !owned && price > 0 && !canAffordBuy && !canAffordRent) {
              card.className += ' unaffordable';
            }
            return card;
          }

          // Render normal weapons first
          normalWeapons.forEach(function(w) {
            const card = createWeaponCard(w, false);
            weaponsGrid.appendChild(card);
          });

          // Render VIP weapons section if any exist
          if (vipWeapons.length > 0) {
            // Insert a header for the Prestige section
            const header = document.createElement('div');
            header.className = 'weapon-category-header';
            header.textContent = 'PRESTIGE';
            weaponsGrid.appendChild(header);
            
            vipWeapons.forEach(function(w) {
              // For prestige weapons, treat them as locked until owned.  If the
              // weapon is not owned, pass true for lockedVip so the card
              // displays "PRESTIGE ONLY" instead of being free.  Once
              // unlocked, lockedVip becomes false and the spawn button is shown.
              const isLockedPrestige = !w.owned;
              const card = createWeaponCard(w, isLockedPrestige);
              weaponsGrid.appendChild(card);
            });
          }
        }

        overlay.style.display = 'flex';
        weaponsShop.style.display = 'block';
      }


    } else if (ev.data.action === 'showAttachmentMenu') {
      console.log('[KOTH] Showing attachment menu:', ev.data);

      const attachmentShop = document.getElementById('attachment-shop');
      const attachmentGrid = document.getElementById('attachment-grid');
      const attachmentPlayerMoney = document.getElementById('attachment-player-money');
      const attachmentWeaponName = document.getElementById('attachment-weapon-name');

      if (attachmentShop && attachmentGrid) {
        // Hide other UIs
        if (teamSelect) teamSelect.style.display = 'none';
        if (menuContainer) menuContainer.style.display = 'none';
        const weaponsShop = document.getElementById('weapons-shop');
        if (weaponsShop) weaponsShop.style.display = 'none';
        const vehiclesShop = document.getElementById('vehicles-shop');
        if (vehiclesShop) vehiclesShop.style.display = 'none';
        const classesSelection = document.getElementById('classes-selection');
        if (classesSelection) classesSelection.style.display = 'none';

        // Update player money
        const moneyValue = typeof ev.data.money === 'number' ? ev.data.money : 0;
        if (attachmentPlayerMoney) {
          attachmentPlayerMoney.textContent = moneyValue.toLocaleString();
        }

        // Update weapon name
        if (attachmentWeaponName) {
          attachmentWeaponName.textContent = ev.data.weaponName || 'Current Weapon';
        }

        // Clear and populate attachment grid
        attachmentGrid.innerHTML = '';

        if (ev.data.attachments && ev.data.attachments.length > 0) {
          ev.data.attachments.forEach(function(attachment) {
            const card = document.createElement('div');
            card.className = 'attachment-card';

            // Check affordability
            const canAfford = moneyValue >= attachment.price;
            if (!canAfford) {
              card.className += ' unaffordable';
            }

            // Use a more robust fallback path for attachments.  When an attachment
            // image fails to load we fall back to a copy of the scope icon in the
            // guns directory.  This ensures the fallback is actually present in
            // our images folder (attachment assets live under images/guns).
            card.innerHTML =
              '<img src="' + attachment.image + '" alt="' + attachment.name + '" class="attachment-image" onerror="this.src=\'images/guns/attachment_scope.png\'">' +
              '<div class="attachment-name">' + attachment.name + '</div>' +
              '<div class="attachment-price">$' + attachment.price + '</div>' +
              '<button class="attachment-buy-btn' + (canAfford ? '' : ' disabled') + '" ' +
              'data-name="' + attachment.name + '" ' +
              'data-component="' + attachment.component + '" ' +
              'data-price="' + attachment.price + '" ' +
              (canAfford ? '' : 'disabled') + '>BUY</button>';
            
            attachmentGrid.appendChild(card);
          });
        } else {
          attachmentGrid.innerHTML = '<div style="text-align: center; color: #ccc; padding: 20px;">No attachments available for this weapon</div>';
        }

        overlay.style.display = 'flex';
        attachmentShop.style.display = 'block';
      }

    } else if (ev.data.action === 'hideAll') {
      console.log('[KOTH] Hiding all');
      overlay.style.display = 'none';
      if (teamSelect) teamSelect.style.display = 'none';
      if (menuContainer) menuContainer.style.display = 'none';

      const weaponsShop = document.getElementById('weapons-shop');
      if (weaponsShop) weaponsShop.style.display = 'none';

      const vehiclesShop = document.getElementById('vehicles-shop');
      if (vehiclesShop) vehiclesShop.style.display = 'none';

      const classesSelection = document.getElementById('classes-selection');
      if (classesSelection) classesSelection.style.display = 'none';

      const attachmentShop = document.getElementById('attachment-shop');
      if (attachmentShop) attachmentShop.style.display = 'none';
    }

  } catch (error) {
    console.error('[KOTH] Error:', error);
  }
});

// Click handler for all buttons
document.addEventListener('click', function(e) {
  try {
    // Team card clicks (new enhanced UI)
    if (e.target.classList.contains('team-card') || e.target.closest('.team-card')) {
      const teamCard = e.target.classList.contains('team-card') ? e.target : e.target.closest('.team-card');
      const team = teamCard.getAttribute('data-team');
      console.log('[KOTH] Team card clicked:', team);

      if (team) {
        // Add visual feedback
        teamCard.style.transform = 'translateY(-5px) scale(0.95)';
        teamCard.style.opacity = '0.8';
        
        // Hide loading indicator
        const loadingIndicator = document.querySelector('.loading-indicator');
        if (loadingIndicator) {
          loadingIndicator.style.opacity = '0';
        }

        fetch('https://' + GetParentResourceName() + '/selectTeam', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ team: team })
        }).catch(function(err) {
          console.error('[KOTH] Team select error:', err);
          // Reset visual feedback on error
          teamCard.style.transform = '';
          teamCard.style.opacity = '';
          if (loadingIndicator) {
            loadingIndicator.style.opacity = '1';
          }
        });
      }
    }

    // Legacy team button clicks (fallback)
    else if (e.target.classList.contains('team-btn')) {
      const team = e.target.getAttribute('data-team');
      console.log('[KOTH] Team clicked:', team);

      if (team) {
        fetch('https://' + GetParentResourceName() + '/selectTeam', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ team: team })
        }).catch(function(err) {
          console.error('[KOTH] Team select error:', err);
        });
      }
    }

    // Action button clicks (old menu system)
    else if (e.target.classList.contains('action-btn')) {
      const type = e.target.getAttribute('data-type');
      const name = e.target.getAttribute('data-name');
      const id = e.target.getAttribute('data-id');
      const weapon = e.target.getAttribute('data-weapon');
      const classType = e.target.getAttribute('data-class');
      const price = e.target.getAttribute('data-price');

      if (type === 'buy' && name) {
        console.log('[KOTH] Buy clicked:', name, 'price:', price, 'button disabled:', e.target.disabled);
        if (e.target.disabled) {
          console.log('[KOTH] Button is disabled, not processing purchase');
          return;
        }
        fetch('https://' + GetParentResourceName() + '/buyVehicle', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ name: name, price: price })
        });
      } else if (type === 'rent' && name) {
        console.log('[KOTH] Rent clicked:', name, 'price:', price, 'button disabled:', e.target.disabled);
        if (e.target.disabled) {
          console.log('[KOTH] Button is disabled, not processing rental');
          return;
        }
        fetch('https://' + GetParentResourceName() + '/rentVehicle', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ name: name, price: price })
        });
      } else if (type === 'weapon' && weapon && classType) {
        console.log('[KOTH] Weapon selected:', weapon, 'for class:', classType);
        fetch('https://' + GetParentResourceName() + '/selectWeapon', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ weapon: weapon, class: classType, price: price })
        });
      } else if (id) {
        console.log('[KOTH] Class selected:', id);
        fetch('https://' + GetParentResourceName() + '/selectClass', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ id: id })
        });
      }
    }

    // Vehicle shop button clicks
    else if (e.target.classList.contains('vehicle-buy-btn') || e.target.classList.contains('vehicle-rent-btn')) {
      const type = e.target.classList.contains('vehicle-buy-btn') ? 'buy' : 'rent';
      const name = e.target.getAttribute('data-name');
      const price = e.target.getAttribute('data-price');

      if (type === 'buy' && name) {
        console.log('[KOTH] Vehicle buy clicked:', name, 'price:', price);
        fetch('https://' + GetParentResourceName() + '/buyVehicle', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ name: name, price: price })
        });
      } else if (type === 'rent' && name) {
        console.log('[KOTH] Vehicle rent clicked:', name, 'price:', price);
        fetch('https://' + GetParentResourceName() + '/rentVehicle', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ name: name, price: price })
        });
      }
    }

    // Vehicle spawn button click for owned vehicles
    else if (e.target.classList.contains('vehicle-spawn-btn')) {
      const name = e.target.getAttribute('data-name');
      if (name) {
        console.log('[KOTH] Vehicle spawn clicked:', name);
        // Use buyVehicle with price 0 to trigger spawning on server
        fetch('https://' + GetParentResourceName() + '/buyVehicle', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ name: name, price: 0 })
        });
      }
    }

    // Weapon shop button clicks
    else if (e.target.classList.contains('weapon-buy-btn') || e.target.classList.contains('weapon-rent-btn')) {
      const weapon = e.target.getAttribute('data-weapon');
      const classType = e.target.getAttribute('data-class');
      const price = e.target.getAttribute('data-price');
      const purchaseType = e.target.getAttribute('data-type'); // 'buy' or 'rent'

      console.log('[KOTH] Weapon', purchaseType, 'clicked:', weapon, 'class:', classType, 'price:', price);
      fetch('https://' + GetParentResourceName() + '/selectWeapon', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          weapon: weapon, 
          class: classType, 
          price: price,
          purchaseType: purchaseType 
        })
      });
    }

    // Class card clicks
    else if (e.target.classList.contains('class-card') || e.target.closest('.class-card')) {
      const card = e.target.classList.contains('class-card') ? e.target : e.target.closest('.class-card');
      const classId = card.getAttribute('data-id');
      const isLocked = card.getAttribute('data-locked') === 'true';

      if (classId) {
        if (isLocked) {
          console.log('[KOTH] Class is locked:', classId);
          // Could add visual feedback here (shake animation, etc.)
          return;
        }

        console.log('[KOTH] Class selected:', classId);

        // Immediately hide classes UI
        const classesSelection = document.getElementById('classes-selection');
        if (classesSelection) {
          console.log('[KOTH] Hiding classes selection UI');
          classesSelection.style.display = 'none';
        }

        fetch('https://' + GetParentResourceName() + '/selectClass', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ id: classId })
        });
      }
    }

    // Attachment purchase button clicks
    else if (e.target.classList.contains('attachment-buy-btn')) {
      const name = e.target.getAttribute('data-name');
      const component = e.target.getAttribute('data-component');
      const price = e.target.getAttribute('data-price');

      console.log('[KOTH] Attachment purchase clicked:', name, 'component:', component, 'price:', price);
      fetch('https://' + GetParentResourceName() + '/purchaseAttachment', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          name: name, 
          component: component, 
          price: price 
        })
      });
    }

    // Close button - handle all close buttons
    else if (e.target.id === 'close-btn' || 
             e.target.id === 'shop-close' || 
             e.target.id === 'vehicle-shop-close' || 
             e.target.id === 'classes-close' ||
             e.target.id === 'attachment-shop-close' ||
             e.target.classList.contains('classes-close') ||
             e.target.classList.contains('attachment-shop-close') ||
             e.target.classList.contains('shop-close')) {
      console.log('[KOTH] Close clicked');
      fetch('https://' + GetParentResourceName() + '/closeMenu', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({})
      });
    }

  } catch (error) {
    console.error('[KOTH] Click error:', error);
  }
});

console.log('[KOTH] Script loaded');

// PERMANENT GAME HUD SYSTEM
let currentGameData = {
  playerName: 'andyflip9',
  playerMoney: 49235,
  playerLevel: 41,
  playerXP: 2847,
  playerMaxXP: 4500,
  playerHealth: 100,
  teamCounts: { red: 0, green: 0, blue: 0 },
  zonePoints: { red: 0, green: 0, blue: 0 },
  kothStatus: {
    active: false,
    controllingTeam: null,
    captureProgress: 0,
    dominantTeam: null,
    isContested: false
  }

  ,isVip: false
};

// Initialize permanent HUD
function initializeGameHUD() {
  console.log('[KOTH] Initializing permanent game HUD');
  console.log('[KOTH] Current team counts:', currentGameData.teamCounts);
  
  // Initialize XP bar to 0% to prevent showing full bar on load
  const xpFillElement = document.getElementById('xp-fill');
  if (xpFillElement) {
    xpFillElement.style.width = '0%';
    console.log('[KOTH] Initialized XP bar to 0%');
  }
  
  // Initialize XP text to show loading state
  const xpTextElement = document.getElementById('xp-text');
  if (xpTextElement) {
    xpTextElement.textContent = '0 / 100 XP';
    console.log('[KOTH] Initialized XP text');
  }
  
  updatePlayerInfo();
  updateTeamCounts();
  updateZonePoints();
  // updateHealthBar() call removed - health system disabled
  updateKothZoneStatus();
}

// Update player information
function updatePlayerInfo() {
  const playerName = document.getElementById('player-name');
  const playerMoney = document.getElementById('player-money-display');
  const playerLevel = document.getElementById('player-level');
  // Kills display (replaces class level)
  const playerKills = document.getElementById('player-kills');

  if (playerName) playerName.textContent = currentGameData.playerName;
  if (playerMoney) playerMoney.textContent = '$' + currentGameData.playerMoney.toLocaleString();
  if (playerLevel) playerLevel.textContent = currentGameData.playerLevel;
  // Update kills count.  The currentGameData.playerKills property is
  // maintained by the server and increased whenever the player gets a kill.
  if (playerKills && typeof currentGameData.playerKills !== 'undefined') {
    playerKills.textContent = currentGameData.playerKills;
  }

  // Show or hide the VIP bonus indicator based on currentGameData.isVip.
  const vipIndicator = document.getElementById('vip-bonus-indicator');
  if (vipIndicator) {
    if (currentGameData.isVip) {
      vipIndicator.style.display = 'flex';
    } else {
      vipIndicator.style.display = 'none';
    }
  }

  // The class XP progress bar has been removed from the main HUD.  Any
  // references to it have been removed here as well.  The XP bar
  // remains only in the weapon shop.
  
  // XP is displayed in the XP bar and text, not as a separate stat
  // Update XP bar and text if we have the data
  if (currentGameData.playerXP && currentGameData.playerMaxXP) {
    const xpTextElement = document.getElementById('xp-text');
    const xpFillElement = document.getElementById('xp-fill');
    
    if (xpTextElement) {
      xpTextElement.textContent = `${currentGameData.playerXP} / ${currentGameData.playerMaxXP} XP`;
    }
    
    if (xpFillElement) {
      const percentage = (currentGameData.playerXP / currentGameData.playerMaxXP) * 100;
      xpFillElement.style.width = `${percentage}%`;
    }
  }
}

// -----------------------------------------------------------------
// Kill feed UI
//
// Messages from the server are forwarded via the 'killFeed' action
// (see the window message handler).  Each message contains the
// killer and victim names and their respective teams.  The helper
// below constructs a DOM element, assigns team colours and fades
// the element out after a few seconds.

// Map team identifiers to CSS colours used in the kill feed.  If a
// team isn't recognised, the default (white) is used.
function getTeamColour(team) {
  switch (team) {
    case 'red': return '#ff5555';
    case 'blue': return '#55aaff';
    case 'green': return '#55ff55';
    default: return '#ffffff';
  }
}

function showKillFeedMessage(data) {
  const container = document.getElementById('kill-feed-container');
  if (!container) return;

  // Create message element
  const msgEl = document.createElement('div');
  msgEl.className = 'kill-feed-message';

  // Killer span
  const killerSpan = document.createElement('span');
  killerSpan.style.color = getTeamColour(data.killerTeam);
  killerSpan.textContent = data.killerName || 'Unknown';

  // Victim span
  const victimSpan = document.createElement('span');
  victimSpan.style.color = getTeamColour(data.victimTeam);
  victimSpan.textContent = data.victimName || 'Unknown';

  // Build message content: [killer] killed [victim]
  msgEl.appendChild(killerSpan);
  msgEl.appendChild(document.createTextNode(' killed '));
  msgEl.appendChild(victimSpan);

  // Append to container
  container.appendChild(msgEl);

  // Fade out and remove after 5 seconds
  setTimeout(() => {
    msgEl.style.opacity = '0';
    setTimeout(() => {
      if (msgEl.parentNode === container) {
        container.removeChild(msgEl);
      }
    }, 500);
  }, 5000);
}

// Update team player counts (person icons section)
function updateTeamCounts() {
  const redPlayers = document.getElementById('red-players');
  const greenPlayers = document.getElementById('green-players');
  const bluePlayers = document.getElementById('blue-players');

  console.log('[KOTH] Updating team counts display:', currentGameData.teamCounts);

  if (redPlayers) {
    redPlayers.textContent = currentGameData.teamCounts.red || 0;
    console.log('[KOTH] Updated red players to:', currentGameData.teamCounts.red || 0);
  }
  if (greenPlayers) {
    greenPlayers.textContent = currentGameData.teamCounts.green || 0;
    console.log('[KOTH] Updated green players to:', currentGameData.teamCounts.green || 0);
  }
  if (bluePlayers) {
    bluePlayers.textContent = currentGameData.teamCounts.blue || 0;
    console.log('[KOTH] Updated blue players to:', currentGameData.teamCounts.blue || 0);
  }
}

// Update zone control points (colored boxes section)
function updateZonePoints() {
  const redPoints = document.getElementById('red-zone-points');
  const greenPoints = document.getElementById('green-zone-points');
  const bluePoints = document.getElementById('blue-zone-points');

  if (redPoints) redPoints.textContent = currentGameData.zonePoints.red || 0;
  if (greenPoints) greenPoints.textContent = currentGameData.zonePoints.green || 0;
  if (bluePoints) bluePoints.textContent = currentGameData.zonePoints.blue || 0;
}


// Health functions REMOVED as requested - user will integrate their own health system later

// Show/Hide KOTH zone status
function showKothZoneStatus() {
  const kothZoneStatus = document.getElementById('koth-zone-status');
  if (kothZoneStatus) {
    kothZoneStatus.style.display = 'block';
    currentGameData.kothStatus.active = true;
    console.log('[KOTH] Zone status shown');
  }
}

function hideKothZoneStatus() {
  const kothZoneStatus = document.getElementById('koth-zone-status');
  if (kothZoneStatus) {
    kothZoneStatus.style.display = 'none';
    currentGameData.kothStatus.active = false;
    console.log('[KOTH] Zone status hidden');
  }
}

// Update KOTH zone status
function updateKothZoneStatus() {
  if (!currentGameData.kothStatus.active) return;

  const status = currentGameData.kothStatus;

  // Update progress bar
  const progressFill = document.getElementById('koth-progress-fill');
  const progressText = document.getElementById('koth-progress-text');

  if (progressFill && progressText) {
    const percentage = Math.round((status.captureProgress / 100) * 100);
    progressFill.style.width = percentage + '%';

    // Update progress bar color based on dominant team
    progressFill.className = 'koth-progress-fill';
    if (status.dominantTeam) {
      progressFill.classList.add(status.dominantTeam);
    }

    // Update progress text
    let statusText = '';
    if (status.controllingTeam) {
      statusText = status.controllingTeam.toUpperCase() + ' CONTROLLED';
    } else if (status.isContested) {
      statusText = 'CONTESTED';
    } else if (status.dominantTeam) {
      statusText = status.dominantTeam.toUpperCase() + ' CAPTURING';
    } else {
      statusText = 'NEUTRAL';
    }

    progressText.textContent = statusText;
  }
}

// Health update functions REMOVED as requested

// Handle money updates from game
function updatePlayerMoney(money) {
  currentGameData.playerMoney = money;
  updatePlayerInfo();
}

// Handle team count updates (for person icons)
function updateTeamCountsData(teamCounts) {
  console.log('[KOTH] Received team counts update:', teamCounts);
  currentGameData.teamCounts = teamCounts;
  // Update the HUD icons
  updateTeamCounts();
  // Also update the team selection UI counts if it is currently visible.
  if (teamCounts) {
    ['red','blue','green'].forEach(function(team) {
      var el = document.getElementById('count-' + team);
      if (el) {
        el.textContent = teamCounts[team] !== undefined ? teamCounts[team] : 0;
      }
    });
  }
}

// Handle zone points updates (for colored boxes)
function updateZonePointsData(zonePoints) {
  currentGameData.zonePoints = zonePoints;
  updateZonePoints();
}


// Handle KOTH status updates
function updateKothStatus(status) {
  currentGameData.kothStatus = {
    ...currentGameData.kothStatus,
    ...status
  };
  updateKothZoneStatus();
}

// DEATH SCREEN SYSTEM
let deathScreenActive = false;
let respawnHoldProgress = 0;
let respawnHoldActive = false;
let bleedoutTimer = 50;
let bleedoutInterval = null;
let respawnInterval = null;

// Show death screen
function showDeathScreen(killerData) {
  const deathScreen = document.getElementById('death-screen');
  const killerIdElement = document.getElementById('killer-id');
  const killerNameElement = document.getElementById('killer-name');
  const bleedoutTimerElement = document.getElementById('bleedout-timer');
  const respawnFill = document.getElementById('respawn-fill');

  if (deathScreen) {
    // Set killer information
    if (killerData) {
      if (killerIdElement) killerIdElement.textContent = killerData.id || '000000';
      if (killerNameElement) killerNameElement.textContent = killerData.name || 'Unknown';
    }

    // Reset values
    bleedoutTimer = 50;
    respawnHoldProgress = 0;
    respawnHoldActive = false;

    if (bleedoutTimerElement) bleedoutTimerElement.textContent = bleedoutTimer;
    if (respawnFill) respawnFill.style.width = '0%';

    // Show death screen
    deathScreen.style.display = 'flex';
    deathScreenActive = true;

    // Start bleedout timer
    startBleedoutTimer();

    console.log('[KOTH] Death screen shown');
  }
}

// Hide death screen
function hideDeathScreen() {
  const deathScreen = document.getElementById('death-screen');
  if (deathScreen) {
    deathScreen.style.display = 'none';
    deathScreenActive = false;

    // Clear timers
    if (bleedoutInterval) {
      clearInterval(bleedoutInterval);
      bleedoutInterval = null;
    }
    if (respawnInterval) {
      clearInterval(respawnInterval);
      respawnInterval = null;
    }

    console.log('[KOTH] Death screen hidden');
  }
}

// Start bleedout countdown
function startBleedoutTimer() {
  if (bleedoutInterval) clearInterval(bleedoutInterval);

  bleedoutInterval = setInterval(function() {
    bleedoutTimer--;
    const bleedoutTimerElement = document.getElementById('bleedout-timer');
    if (bleedoutTimerElement) {
      bleedoutTimerElement.textContent = bleedoutTimer;
    }

    // Auto respawn when timer reaches 0
    if (bleedoutTimer <= 0) {
      console.log('[KOTH] Bleedout timer expired, auto respawning');
      clearInterval(bleedoutInterval);

      // Trigger respawn
      fetch('https://' + GetParentResourceName() + '/playerRespawn', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type: 'bleedout' })
      });
    }
  }, 1000);
}

// Respawn progress is now handled entirely by the server
// The server sends updateRespawnProgress messages with the exact progress percentage

// E key handling removed - respawn is now fully controlled by the server
// The server sends updateRespawnProgress messages to update the UI

// Handle death screen specific events from the game
window.addEventListener('message', function(event) {
  const data = event.data;

  // Handle death screen specific events
  if (data.action === 'updateBleedoutTimer') {
    const timerElement = document.getElementById('bleedout-timer');
    if (timerElement) {
      timerElement.textContent = data.time;
      bleedoutTimer = data.time;
    }
    return;
  }
  
  if (data.action === 'updateRespawnProgress') {
    const progressElement = document.getElementById('respawn-fill');
    if (progressElement) {
      // Use the progress value directly from the server (already 0-100)
      progressElement.style.width = data.progress + '%';
      console.log('[KOTH] Respawn progress updated from server:', data.progress + '%');
    }
    return;
  }
});

// FIXED KILL REWARD SYSTEM - Matching HTML Elements
function showKillReward(data) {
  console.log('[KOTH] showKillReward function called with data:', data);
  
  const popup = document.getElementById('kill-reward-popup');
  const rewardValue = document.getElementById('kill-reward-value');
  const xpValue = document.getElementById('kill-xp-value');
  const zoneIndicator = document.getElementById('kill-reward-zone-indicator');

  console.log('[KOTH] Kill reward elements found:', {
    popup: !!popup,
    rewardValue: !!rewardValue,
    xpValue: !!xpValue,
    zoneIndicator: !!zoneIndicator
  });

  if (popup && rewardValue && xpValue) {
    // Set reward data - matching the HTML structure
    rewardValue.textContent = `$${data.money || 0}`;
    xpValue.textContent = data.xp || 0;

    // Show/hide zone bonus indicator
    if (zoneIndicator) {
      if (data.inZone) {
        zoneIndicator.style.display = 'block';
        popup.classList.add('zone-kill');
        console.log('[KOTH] Zone kill bonus shown');
      } else {
        zoneIndicator.style.display = 'none';
        popup.classList.remove('zone-kill');
        console.log('[KOTH] Normal kill - no zone bonus');
      }
    }

    // Add animation class for enhanced effect
    popup.classList.add('kill-reward-show');

    // Make sure popup is visible with proper styling
    popup.style.display = 'block';
    popup.style.opacity = '1';
    popup.style.visibility = 'visible';
    popup.style.zIndex = '10000';
    popup.style.position = 'fixed';
    
    // Force a reflow to ensure the element is rendered
    popup.offsetHeight;
    
    console.log('[KOTH] Kill reward popup displayed successfully');
    
    // Auto hide after 4 seconds with enhanced animation
    setTimeout(function() {
      popup.classList.add('kill-reward-hide');
      popup.style.transition = 'opacity 0.5s ease-out, transform 0.5s ease-out';
      popup.style.opacity = '0';
      popup.style.transform = 'translateY(-20px)';
      
      setTimeout(function() {
        popup.style.display = 'none';
        popup.style.visibility = 'hidden';
        popup.style.transform = '';
        popup.classList.remove('kill-reward-show', 'kill-reward-hide', 'zone-kill');
        console.log('[KOTH] Kill reward popup hidden');
      }, 500);
    }, 4000);

    console.log('[KOTH] Kill reward popup shown - Money: $' + (data.money || 0) + ', XP: ' + (data.xp || 0) + ', In Zone: ' + (data.inZone ? 'YES' : 'NO'));
  } else {
    console.error('[KOTH] Kill reward popup elements not found!');
    console.error('popup:', !!popup, 'rewardValue:', !!rewardValue, 'xpValue:', !!xpValue);
    
    // Debug: List all elements with kill-reward in their ID
    const allElements = document.querySelectorAll('[id*="kill"]');
    console.log('[KOTH] All elements with "kill" in ID:', Array.from(allElements).map(el => el.id));
  }
}

function showLevelUp(data) {
  const popup = document.getElementById('levelup-popup');
  const levelOld = document.getElementById('level-old');
  const levelNew = document.getElementById('level-new');

  if (popup) {
    // Set level data
    if (levelOld) levelOld.textContent = data.oldLevel || 1;
    if (levelNew) levelNew.textContent = data.newLevel || 2;

    // Show popup
    popup.style.display = 'block';

    // Auto hide after 4 seconds
    setTimeout(function() {
      popup.style.display = 'none';
    }, 4000);

    console.log('[KOTH] Level up popup shown - ' + (data.oldLevel || 1) + ' → ' + (data.newLevel || 2));
  }
}

function updatePlayerData(data) {
  // Store player data for future use
  if (data) {
    console.log('[KOTH] Player data updated:', data);

    // Update global game data
    if (data.player_name) currentGameData.playerName = data.player_name;
    if (typeof data.money === 'number') {
      currentGameData.playerMoney = data.money;
      // Store for shop use
      window.lastKnownPlayerMoney = data.money;
    }
    if (typeof data.level === 'number') currentGameData.playerLevel = data.level;
    if (typeof data.xp === 'number') currentGameData.playerXP = data.xp;
    if (typeof data.kills === 'number') currentGameData.playerKills = data.kills;

    // If the server provided the player's current class level, store it
    // in the global state.  This is used to display the class level in
    // the HUD and to gate weapons/vehicles.
    if (typeof data.classLevel === 'number') {
      currentGameData.classLevel = data.classLevel;
    }

    // Persist class XP and max XP when provided.  These values will
    // drive the class XP bar in the HUD.  If they are absent, the
    // existing values remain unchanged.
    if (typeof data.classXP === 'number') {
      currentGameData.classXP = data.classXP;
    }
    if (typeof data.classMaxXP === 'number') {
      currentGameData.classMaxXP = data.classMaxXP;
    }

    // Store VIP status.  The server includes isVip when sending
    // playerData.  If undefined, default to false.  This value will
    // toggle the VIP bonus indicator in the HUD.
    if (typeof data.isVip !== 'undefined') {
      currentGameData.isVip = !!data.isVip;
    }

    // Update HUD elements using original IDs
    const playerNameElement = document.getElementById('player-name');
    const moneyElement = document.getElementById('player-money-display');
    const levelElement = document.getElementById('player-level');
    const killsElement = document.getElementById('player-kills');
    const xpTextElement = document.getElementById('xp-text');
    const xpFillElement = document.getElementById('xp-fill');

    if (playerNameElement && data.player_name) {
      playerNameElement.textContent = data.player_name;
      console.log('[KOTH] Updated player name to:', data.player_name);
    }

    if (moneyElement && typeof data.money === 'number') {
      const oldValue = moneyElement.textContent;
      moneyElement.textContent = `$${data.money.toLocaleString()}`;
      console.log('[KOTH] Updated money from', oldValue, 'to:', `$${data.money.toLocaleString()}`);

      // Visual feedback - flash the element
      moneyElement.style.color = '#7bed9f';
      setTimeout(() => {
        moneyElement.style.color = '';
      }, 1000);
    } else {
      console.log('[KOTH] Money update failed - element:', moneyElement, 'data.money:', data.money, 'type:', typeof data.money);
    }

    if (levelElement && typeof data.level === 'number') {
      levelElement.textContent = data.level;
      console.log('[KOTH] Updated level to:', data.level);
    }

    if (killsElement && typeof data.kills === 'number') {
      killsElement.textContent = data.kills;
      console.log('[KOTH] Updated kills to:', data.kills);
    }

    // Update prestige display on the HUD.  The server includes
    // prestigeRank in the updatePlayerData payload.  If present, update
    // the prestige value in the HUD.  If absent, leave the existing
    // value untouched.
    const prestigeElement = document.getElementById('player-prestige');
    if (prestigeElement && typeof data.prestigeRank === 'number') {
      prestigeElement.textContent = data.prestigeRank;
      // Also store in global state for future reference
      currentGameData.prestigeRank = data.prestigeRank;
    }

    // Update XP progress bar and text - FIXED VERSION
    if (typeof data.xp === 'number' && typeof data.level === 'number') {
      // Level system EXACTLY matches server-side calculation
      const levels = [
        {level: 1, required: 0},
        {level: 2, required: 100},
        {level: 3, required: 250},
        {level: 4, required: 500},
        {level: 5, required: 1000},
        {level: 6, required: 1750},
        {level: 7, required: 2750},
        {level: 8, required: 4000},
        {level: 9, required: 6000},
        {level: 10, required: 8500},
        {level: 11, required: 11000},
        {level: 12, required: 14000},
        {level: 13, required: 17500},
        {level: 14, required: 21500},
        {level: 15, required: 26000},
        {level: 16, required: 31000},
        {level: 17, required: 36500},
        {level: 18, required: 42500},
        {level: 19, required: 49000},
        {level: 20, required: 56000},
        {level: 25, required: 100000},
        {level: 30, required: 150000},
        {level: 40, required: 250000},
        {level: 50, required: 400000}
      ];

      // Find current and next level requirements
      let currentLevelXP = 0;
      let nextLevelXP = 100; // Default for level 2
      let foundLevel = false;

      console.log('[KOTH] Finding XP requirements for level', data.level);

      // First check if level is in our defined levels
      for (let i = 0; i < levels.length; i++) {
        if (levels[i].level === data.level) {
          currentLevelXP = levels[i].required;
          foundLevel = true;
          
          // Find next level requirement
          if (i + 1 < levels.length) {
            nextLevelXP = levels[i + 1].required;
          } else {
            // Beyond defined levels, calculate based on formula
            if (data.level >= 50) {
              // After level 50, each level requires 10000 more XP
              nextLevelXP = currentLevelXP + 10000;
            } else {
              // Between defined levels, interpolate
              nextLevelXP = currentLevelXP + 10000;
            }
          }
          console.log('[KOTH] Found exact level match:', data.level, 'Current XP req:', currentLevelXP, 'Next XP req:', nextLevelXP);
          break;
        } else if (i + 1 < levels.length && levels[i].level < data.level && levels[i + 1].level > data.level) {
          // Level is between two defined levels, interpolate
          const levelDiff = levels[i + 1].level - levels[i].level;
          const xpDiff = levels[i + 1].required - levels[i].required;
          const xpPerLevel = xpDiff / levelDiff;
          const levelsFromBase = data.level - levels[i].level;
          currentLevelXP = Math.floor(levels[i].required + (xpPerLevel * levelsFromBase));
          nextLevelXP = Math.floor(currentLevelXP + xpPerLevel);
          foundLevel = true;
          console.log('[KOTH] Interpolated level', data.level, 'between', levels[i].level, 'and', levels[i + 1].level);
          console.log('[KOTH] Interpolation - Current XP req:', currentLevelXP, 'Next XP req:', nextLevelXP);
          break;
        }
      }
      
      // If level not found in table
      if (!foundLevel) {
        console.log('[KOTH] Level', data.level, 'not found in table, using fallback calculation');
        
        // For levels 16-19, interpolate between 15 and 20
        if (data.level >= 16 && data.level <= 19) {
          const level15XP = 26000;
          const level20XP = 56000;
          const xpDiff = level20XP - level15XP; // 30000 XP difference
          const levelDiff = 20 - 15; // 5 levels
          const xpPerLevel = xpDiff / levelDiff; // 6000 XP per level
          const levelsFrom15 = data.level - 15;
          currentLevelXP = level15XP + (levelsFrom15 * xpPerLevel);
          nextLevelXP = currentLevelXP + xpPerLevel;
          console.log('[KOTH] Calculated for level', data.level, '- Current XP req:', currentLevelXP, 'Next XP req:', nextLevelXP);
        } else if (data.level > 50) {
          // After level 50, each level requires 10000 more XP
          const extraLevels = data.level - 50;
          currentLevelXP = 400000 + (extraLevels * 10000);
          nextLevelXP = currentLevelXP + 10000;
          console.log('[KOTH] High level calculation - Current XP req:', currentLevelXP, 'Next XP req:', nextLevelXP);
        } else {
          // Fallback for any other missing levels
          console.log('[KOTH] WARNING: Could not calculate XP for level', data.level);
          currentLevelXP = 0;
          nextLevelXP = 100;
        }
      }

      const progressXP = data.xp - currentLevelXP;
      const neededXP = nextLevelXP - currentLevelXP;

      // Make sure progress doesn't exceed needed XP
      const displayProgressXP = Math.max(0, Math.min(progressXP, neededXP));
      const progressPercentage = neededXP > 0 ? (displayProgressXP / neededXP) * 100 : 100;

      // Update XP progress bar using ID (as per HTML structure)
      const xpFillElement = document.getElementById('xp-fill');
      if (xpFillElement) {
        xpFillElement.style.width = `${progressPercentage}%`;
        console.log('[KOTH] Updated XP bar to:', `${progressPercentage}%`);
      } else {
        console.log('[KOTH] ERROR: Could not find xp-fill element!');
      }

      // Update XP text below the bar using ID
      const xpTextElement = document.getElementById('xp-text');
      if (xpTextElement) {
        xpTextElement.textContent = `${displayProgressXP} / ${neededXP} XP`;
        console.log('[KOTH] Updated XP text to:', `${displayProgressXP} / ${neededXP} XP`);
      } else {
        console.log('[KOTH] ERROR: Could not find xp-text element!');
      }

      console.log('[KOTH] XP Progress - Total XP:', data.xp, 'Level:', data.level, 'Progress:', displayProgressXP, 'Needed:', neededXP, 'Percentage:', progressPercentage);
    }

    // Toggle the VIP bonus indicator based on currentGameData.isVip.  The
    // indicator element is only visible when the player possesses
    // the configured Discord role.  Without this toggle the indicator
    // would never appear or disappear when VIP status changes.
    const vipIndicator = document.getElementById('vip-bonus-indicator');
    if (vipIndicator) {
      if (currentGameData.isVip) {
        vipIndicator.style.display = 'flex';
      } else {
        vipIndicator.style.display = 'none';
      }
    }

    console.log('[KOTH] HUD updated with new player data');

    // Also update any open shops with new money data
    updateOpenShops(data.money);
  }
}

// Function to update any open shops with new money data
function updateOpenShops(newMoney) {
  // Update vehicle shop if open
  const vehiclesShop = document.getElementById('vehicles-shop');
  if (vehiclesShop && vehiclesShop.style.display === 'block') {
    const vehiclePlayerMoney = document.getElementById('vehicle-player-money');
    if (vehiclePlayerMoney) {
      vehiclePlayerMoney.textContent = newMoney.toLocaleString();
      console.log('[KOTH] Updated vehicle shop money to:', newMoney);
    }
    
    // Update button states
    const vehicleButtons = document.querySelectorAll('.vehicle-buy-btn, .vehicle-rent-btn');
    vehicleButtons.forEach(btn => {
      const price = parseInt(btn.getAttribute('data-price'));
      const canAfford = newMoney >= price;
      btn.disabled = !canAfford;
      if (canAfford) {
        btn.classList.remove('disabled');
      } else {
        btn.classList.add('disabled');
      }
    });
  }
  
  // Update weapon shop if open
  const weaponsShop = document.getElementById('weapons-shop');
  if (weaponsShop && weaponsShop.style.display === 'block') {
    const playerMoney = document.getElementById('player-money');
    if (playerMoney) {
      playerMoney.textContent = newMoney.toLocaleString();
      console.log('[KOTH] Updated weapon shop money to:', newMoney);
    }
    
    // Update button states
    const weaponButtons = document.querySelectorAll('.weapon-buy-btn');
    weaponButtons.forEach(btn => {
      const price = parseInt(btn.getAttribute('data-price'));
      const canAfford = newMoney >= price;
      btn.disabled = !canAfford;
      if (canAfford) {
        btn.classList.remove('disabled');
      } else {
        btn.classList.add('disabled');
      }
    });
  }
}

// Test function to verify elements exist
function testHUDElements() {
  console.log('[KOTH] Testing HUD elements:');
  console.log('  player-name:', document.getElementById('player-name'));
  console.log('  player-money-display:', document.getElementById('player-money-display'));
  console.log('  player-level:', document.getElementById('player-level'));
  console.log('  player-xp:', document.getElementById('player-xp'));
}

// Test functions removed - they were interfering with real player data

// Initialize HUD when page loads
document.addEventListener('DOMContentLoaded', function() {
  console.log('[KOTH] DOM loaded, initializing HUD');
  initializeGameHUD();
  testHUDElements();

  // Initialise the prestige menu after the DOM has loaded.  This binds
  // button click handlers and prepares the weapon list.  Without this
  // call the prestige menu buttons will not function.
  initializePrestigeMenu();
});

/*
 * Prestige Shop Implementation
 *
 * The prestige system allows players to reset their level upon reaching
 * level 50 in exchange for tokens that can be spent on exclusive
 * weapons and vehicles.  This section defines the client‑side logic
 * for displaying the prestige menu, handling button presses and
 * sending the appropriate NUI callbacks back to the Lua code.  The
 * server validates all actions and maintains token balances.
 */

// Define the list of available prestige weapons.  These weapons are
// identical to the VIP weapons from the standard weapon shop.  Each
// entry defines the internal weapon ID (used by the game), a
// human‑readable name and the path to an image.  If you wish to add
// more prestige weapons in future, add entries here.  The images are
// referenced relative to the html folder.
const prestigeWeapons = [
  { id: 'WEAPON_FAMASMK2', name: 'FAMAS Mk II', img: 'images/guns/FAMAS.png' },
  { id: 'WEAPON_SCARMK2', name: 'SCAR Mk II', img: 'images/guns/SCAR.png' },
  { id: 'WEAPON_L85A2',   name: 'L85A2',      img: 'images/guns/Advanced Rifle.png' },
  { id: 'WEAPON_SCAR',     name: 'SCAR',       img: 'images/guns/SCAR.png' },
  { id: 'WEAPON_M4A1',     name: 'M4A1',       img: 'images/guns/M4A1.png' },
  { id: 'WEAPON_FAMAS',    name: 'FAMAS',      img: 'images/guns/FAMAS.png' }
];

// Define available prestige vehicles.  Each entry contains a unique id
// (matching the server event parameter), a human‑friendly name and
// the path to its thumbnail image.  Players spend a vehicle token to
// permanently unlock one of these vehicles.
const prestigeVehicles = [
  { id: 'Insurgent2', name: 'Insurgent2', img: 'images/vehicles/insurgent2.png' },
  { id: 'Ramp Buggy', name: 'Ramp Buggy', img: 'images/vehicles/dune4.png' }
];

// Build the prestige menu and attach event listeners.  This function
// should be called once after the DOM has loaded.  It constructs the
// list of prestige weapons and wires up the buttons to send NUI
// callbacks back to the Lua environment.  The menu remains hidden
// until the server sends an `openPrestigeMenu` message.
function initializePrestigeMenu() {
  const prestigeMenu = document.getElementById('prestige-menu');
  const prestigeWeaponSection = document.getElementById('prestige-weapon-section');
  const prestigeWeaponsList = document.getElementById('prestige-weapons-list');
  const prestigeButton = document.getElementById('prestige-button');
  const buyWeaponButton = document.getElementById('prestige-buy-weapon-button');
  const buyVehicleButton = document.getElementById('prestige-buy-vehicle-button');
  const prestigeVehicleSection = document.getElementById('prestige-vehicle-section');
  const prestigeVehiclesList = document.getElementById('prestige-vehicles-list');
  const closeButton = document.getElementById('prestige-close-button');

  // Build the list of prestige weapons.  Each button triggers a
  // prestigeAction callback with action=buyWeapon and the chosen
  // weapon.  After sending the callback we close the menu.  If the
  // server rejects the purchase it will notify via chat and the
  // prestige data will be refreshed.
  if (prestigeWeaponsList) {
    prestigeWeaponsList.innerHTML = '';
    prestigeWeapons.forEach(function(w) {
      const btn = document.createElement('button');
      btn.className = 'prestige-weapon-button';
      btn.innerHTML = `<img src="${w.img}" alt="${w.name}" style="width:24px; height:24px; margin-right:5px; vertical-align:middle;">${w.name}`;
      btn.dataset.weapon = w.id;
      btn.addEventListener('click', function() {
        // Send NUI callback to purchase the weapon.  The server
        // performs validation and will deduct a token if successful.
        fetch(`https://${GetParentResourceName()}/prestigeAction`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json; charset=UTF-8' },
          body: JSON.stringify({ action: 'buyWeapon', weapon: w.id })
        }).catch(function(err) {
          console.error('[KOTH] Prestige weapon purchase fetch error:', err);
        });
        // Hide the weapon selection and close the menu.  The server
        // will send updated token counts which will refresh the HUD.
        closePrestigeMenu();
      });
      prestigeWeaponsList.appendChild(btn);
    });
  }

  // Build the list of prestige vehicles.  Each button triggers a
  // prestigeAction callback with action=buyVehicle and the chosen
  // vehicle id.  After sending the callback we close the menu.  The
  // server performs validation and will deduct a token if successful.
  if (prestigeVehiclesList) {
    prestigeVehiclesList.innerHTML = '';
    prestigeVehicles.forEach(function(v) {
      const btn = document.createElement('button');
      btn.className = 'prestige-vehicle-button';
      btn.innerHTML = `<img src="${v.img}" alt="${v.name}" style="width:24px; height:24px; margin-right:5px; vertical-align:middle;">${v.name}`;
      btn.dataset.vehicle = v.id;
      btn.addEventListener('click', function() {
        // Send NUI callback to purchase the vehicle with a specific id
        fetch(`https://${GetParentResourceName()}/prestigeAction`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json; charset=UTF-8' },
          body: JSON.stringify({ action: 'buyVehicle', vehicle: v.id })
        }).catch(function(err) {
          console.error('[KOTH] Prestige vehicle purchase fetch error:', err);
        });
        // Hide the vehicle selection and close the menu.  The server will
        // update token counts and refresh the HUD.
        closePrestigeMenu();
      });
      prestigeVehiclesList.appendChild(btn);
    });
  }

  // Prestige button: triggers a prestige attempt.  The server will
  // validate the player's level and current prestige rank.  If
  // successful, it will reset the player's level and award tokens.
  if (prestigeButton) {
    prestigeButton.addEventListener('click', function() {
      fetch(`https://${GetParentResourceName()}/prestigeAction`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json; charset=UTF-8' },
        body: JSON.stringify({ action: 'prestige' })
      }).catch(function(err) {
        console.error('[KOTH] Prestige fetch error:', err);
      });
    });
  }

  // Buy vehicle button: spends a prestige vehicle token to unlock the
  // Prestige vehicle button: toggles the vehicle selection panel.  This
  // panel allows the player to choose which prestige vehicle they wish
  // to unlock.  If the player has no vehicle tokens the button will
  // be disabled by openPrestigeMenu().  When the panel is shown the
  // player can click on a vehicle to purchase it.
  if (buyVehicleButton) {
    buyVehicleButton.addEventListener('click', function() {
      if (!prestigeVehicleSection) return;
      const isVisible = prestigeVehicleSection.style.display !== 'none';
      prestigeVehicleSection.style.display = isVisible ? 'none' : 'block';
    });
  }

  // Buy weapon button: toggles the weapon selection panel.  This
  // panel allows the player to choose which prestige weapon they
  // wish to unlock.  If the player has no weapon tokens the button
  // will be disabled by openPrestigeMenu().
  if (buyWeaponButton) {
    buyWeaponButton.addEventListener('click', function() {
      if (!prestigeWeaponSection) return;
      const isVisible = prestigeWeaponSection.style.display !== 'none';
      prestigeWeaponSection.style.display = isVisible ? 'none' : 'block';
    });
  }

  // Close button: simply hide the menu.  The server will be asked to
  // close the menu via NUI callback when appropriate.  Here we
  // directly call closePrestigeMenu() because the server callback
  // will be triggered by the fetch in other handlers (prestige or
  // purchases).  If the player simply closes the menu without an
  // action we still want to relinquish focus.
  if (closeButton) {
    closeButton.addEventListener('click', function() {
      // Notify Lua code that the menu was closed without action.  We
      // send a dummy action so the client can release focus.  The
      // server ignores unrecognised actions.
      fetch(`https://${GetParentResourceName()}/prestigeAction`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json; charset=UTF-8' },
        body: JSON.stringify({ action: 'close' })
      }).catch(function(err) {
        console.error('[KOTH] Prestige close fetch error:', err);
      });
      closePrestigeMenu();
    });
  }
}

// Display the prestige shop overlay with updated data.  The data
// object is provided by the Lua code and contains the player's
// current prestige rank, weapon tokens, vehicle tokens and level.
// This function updates the UI elements, enables/disables buttons
// accordingly and shows the menu.  It also resets the weapon
// selection panel to be hidden.
function openPrestigeMenu(data) {
  const menu = document.getElementById('prestige-menu');
  if (!menu) return;
  const rankEl = document.getElementById('prestige-rank');
  const weaponTokensEl = document.getElementById('prestige-weapon-tokens');
  const vehicleTokensEl = document.getElementById('prestige-vehicle-tokens');
  const prestigeBtn = document.getElementById('prestige-button');
  const buyWeaponBtn = document.getElementById('prestige-buy-weapon-button');
  const buyVehicleBtn = document.getElementById('prestige-buy-vehicle-button');
  const weaponSection = document.getElementById('prestige-weapon-section');
  const vehicleSection = document.getElementById('prestige-vehicle-section');
  // Update displayed numbers
  if (rankEl) rankEl.textContent = data.prestigeRank || 0;
  if (weaponTokensEl) weaponTokensEl.textContent = data.weaponTokens || 0;
  if (vehicleTokensEl) vehicleTokensEl.textContent = data.vehicleTokens || 0;
  // Enable or disable the prestige button.  A player can prestige if
  // they are at least level 50 and have not reached the maximum
  // prestige rank of 5.  The server will enforce these rules but
  // disabling the button provides immediate feedback.
  if (prestigeBtn) {
    const canPrestige = (data.playerLevel || 0) >= 50 && (data.prestigeRank || 0) < 5;
    prestigeBtn.disabled = !canPrestige;
  }
  // Enable/disable the buy weapon and vehicle buttons based on token
  // counts.  If the player has no tokens the buttons are disabled.
  if (buyWeaponBtn) {
    buyWeaponBtn.disabled = (data.weaponTokens || 0) <= 0;
  }
  if (buyVehicleBtn) {
    buyVehicleBtn.disabled = (data.vehicleTokens || 0) <= 0;
  }
  // Hide the weapon selection panel whenever the menu opens
  if (weaponSection) {
    weaponSection.style.display = 'none';
  }
  // Hide the vehicle selection panel whenever the menu opens
  if (vehicleSection) {
    vehicleSection.style.display = 'none';
  }
  // Show the menu
  menu.style.display = 'flex';
}

// Hide the prestige shop overlay.  This function simply hides the
// container; actual data refresh and focus handling are done in the
// Lua code when it receives the prestigeAction callback.  Calling
// this function ensures the overlay does not persist if the user
// closes the menu manually.
function closePrestigeMenu() {
  const menu = document.getElementById('prestige-menu');
  if (menu) {
    menu.style.display = 'none';
  }
}

// WEAPON HOTBAR REMOVED - Using separate hotbar resource instead
