fx_version 'cerulean'
game 'gta5'

-- Ensure the koth_classes resource is started before this one so that
-- client exports (e.g. GetCurrentClass) are available.  Without
-- declaring this dependency the medic revive script may fail to
-- detect the player's class correctly if koth_classes has not yet
-- initialised.
dependency 'koth_classes'

author 'You'
description 'KOTH Full UI'
version '1.3.0'

ui_page 'html/ui_simple.html'

client_scripts {
  'client.lua',
  'client_death.lua',
  'test_ui_interaction.lua'
}

server_scripts {
  '@oxmysql/lib/MySQL.lua',
  'server.lua'
  -- NOTE: The kill_test.lua file contained a number of debugging commands and is no longer
  -- loaded on the server.  All functionality that players need (e.g. vehicle spawning,
  -- XP and money updates, admin tools, etc.) remains in server.lua.  Removing this file
  -- from the manifest prevents the unused test code from running and eliminates errors
  -- related to undefined variables such as `args`.
}

files {
  'html/ui.html',
  'html/ui_simple.html',
  'html/style.css',
  'html/script.js',
  'html/script_death.js',
  'html/ui_fix.js',
  'html/attachment_menu.html',
  'html/attachment_menu.css',
  'html/attachment_menu.js',
  'html/images/**/*',
  '../Vimages/**/*'
  ,
  -- Database schemas
  'sql_create_weapon_ownership_table.sql'
}
