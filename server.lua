print('[KOTH] Server loading...')

-- MONEY AND XP SYSTEM - Define at top so all handlers can access it
local playerData = {} -- Cache for player data

-- VIP roles.  A player is considered VIP if they possess ANY of
-- these identifiers on their Discord account.  You can specify
-- numeric IDs or role names.  If using numeric IDs, enclose them
-- as strings.  Add additional entries as needed.  The first entry
-- corresponds to the Discord role ID provided by the server owner.
local VIP_ROLES = {
  '1402275621543084092',        -- Role ID
  'Vantage Supporter [VIP Access]' -- Role name
}

---
-- Check whether a player has the configured VIP Discord role.  This
-- implementation uses <PERSON><PERSON>’s discord roles resource if present. It
-- gracefully falls back to false when the export is missing or
-- encounters an error.  You can replace this logic with another
-- permission system as needed.
-- @param src number The server ID of the player to check
-- @return boolean true if the player has the VIP role, false otherwise
local function IsPlayerVIP(src)
  -- Attempt to check the player's Discord roles using the
  -- badger_discord_roles export.  We first try to fetch all roles
  -- associated with the player via GetRoles (if available).  If
  -- GetRoles is unavailable or fails, we fall back to checking each
  -- configured role individually via IsRolePresent.  If any role
  -- matches, the player is VIP.
  local function checkRoles(roles)
    if type(roles) ~= 'table' then return false end
    for _, r in pairs(roles) do
      for _, vip in ipairs(VIP_ROLES) do
        -- Some role objects may have an id or name field.  Compare
        -- against both the raw value and the id/name fields.
        if tostring(r) == tostring(vip) or (type(r) == 'table' and (tostring(r.id) == tostring(vip) or tostring(r.name) == tostring(vip))) then
          return true
        end
      end
    end
    return false
  end
  -- Try to get all roles at once
  local ok, roles = pcall(function()
    return exports['badger_discord_roles']:GetRoles(src)
  end)
  if ok and roles then
    if checkRoles(roles) then
      return true
    end
  end
  -- Fallback: check each configured role via IsRolePresent
  for _, vip in ipairs(VIP_ROLES) do
    local ok2, result2 = pcall(function()
      return exports['badger_discord_roles']:IsRolePresent(src, vip)
    end)
    if ok2 and result2 then
      return true
    end
  end
  return false
end
print(('[KOTH] DEBUG: playerData initialized as %s'):format(type(playerData)))

-- Initial team spawn definitions.  These values will be overridden
-- immediately after mapLocations are defined, so the specific values
-- here do not matter.  They serve only as a placeholder until
-- teamSpawns is reassigned to the spawns of the first map.
local teamSpawns = {
  red   = { x = 0.0, y = 0.0, z = 0.0, heading = 0.0 },
  blue  = { x = 0.0, y = 0.0, z = 0.0, heading = 0.0 },
  green = { x = 0.0, y = 0.0, z = 0.0, heading = 0.0 }
}

-- Team player tracking
local playerTeams = {} -- Track each player's team
local teamCounts = { red = 0, blue = 0, green = 0 } -- Live team counts
local zonePoints = { red = 0, blue = 0, green = 0 } -- Zone control points

-- Track the number of kills each player has accumulated during the current
-- KOTH round.  This table is reset whenever the map rotates.  Entries
-- are keyed by the player's server ID (number) and the values are the
-- integer count of kills they have achieved since the round began.  The
-- leaderboard presented to clients uses this data to determine the top
-- performers for the current map.  See the handler for
-- 'koth:requestLeaderboard' below for details.
local roundKills = {}

--[[
  Suppress the default join/leave messages printed by the chat resource.  When
  players connect or disconnect, FiveM's chat resource sends a message
  like "^3PlayerName^7 joined the game" or "^3PlayerName^7 left the game".
  These messages clutter the chat during matches.  By handling the
  `chatMessage` event and cancelling it when the message contains the
  join/leave phrases, we prevent the messages from being broadcast to
  players.  This does not affect server logs.
]]
AddEventHandler('chatMessage', function(source, name, msg)
  if msg then
    local lower = string.lower(msg)
    if string.find(lower, 'joined the game', 1, true) or string.find(lower, 'left the game', 1, true) then
      CancelEvent()
      return
    end
  end
end)

-- Track which players are currently standing inside the KOTH capture area.
-- Indexed by team colour, this table maps server IDs to a boolean
-- indicating the player is inside the zone.  We update this in the
-- playerEnteredZone/playerLeftZone events.  The data is used to award
-- periodic zone control bonuses (XP/money) to players physically
-- occupying the objective when their team controls it.
local zonePlayers = { red = {}, blue = {}, green = {} }

--[[
Award a zone control bonus to all players currently occupying the KOTH
objective for the specified team.  When a team holds the zone
uncontested, each member inside the capture area receives a small XP
and money bonus every second.  This function iterates over the set
of server IDs stored in zonePlayers[team], updates their playerData
fields and sends the updated data to the clients.  It also handles
level‑up detection and triggers the appropriate client events when a
level increases.  If no players are inside the zone or playerData
for a given ID is missing, the function safely skips awards for
that player.

Parameters:
  team (string): One of 'red', 'blue' or 'green' identifying the
                 controlling team.
]]
local function AwardZoneControlBonus(team)
  if not team or not zonePlayers or not zonePlayers[team] then return end
  -- Define the bonus amounts for zone control.  These values were
  -- requested by the server owner.  Players earn these rewards once
  -- per second while their team holds the objective uncontested.
  local bonusXP = 50
  local bonusMoney = 50
  for playerId, _ in pairs(zonePlayers[team]) do
    local id = tonumber(playerId)
    -- Ensure we have valid player data for this ID
    local pdata = playerData[id]
    if pdata then
      local oldLevel = pdata.level or 0
      -- Award XP and money
      pdata.money = (pdata.money or 0) + bonusMoney
      pdata.xp = (pdata.xp or 0) + bonusXP
      -- Recalculate level
      pdata.level = CalculateLevel(pdata.xp)
      -- Trigger level up event if applicable
      if pdata.level > oldLevel then
        TriggerClientEvent('koth:levelUp', id, { newLevel = pdata.level, oldLevel = oldLevel })
      end
      -- Immediately update the client HUD with new stats
      TriggerClientEvent('koth:updatePlayerData', id, pdata)
      -- Persist to database asynchronously
      SavePlayerData(id)
    end
  end
end

--[[
  Award a presence bonus to all players currently inside the KOTH capture
  area.  Unlike AwardZoneControlBonus which rewards only the controlling
  team once per second, this function ignores which team is holding the
  hill and instead grants a small XP and money bonus to everyone
  physically located in the zone.  This bonus is paid out on a much
  longer interval (90 seconds by default) and is intended to encourage
  players to contest the objective even when their team is not in
  control.

  The bonus amounts are configured below.  When awarding, the
  function iterates over the zonePlayers table (populated by
  koth:playerEnteredZone/koth:playerLeftZone events) and updates
  each player's cached data.  It then sends updates to the
  clients and persists the changes.
]]
local function AwardZonePresenceBonus()
  -- Define the presence bonus amounts as requested by the server owner
  local bonusXP = 50
  local bonusMoney = 50
  -- Ensure zonePlayers is defined
  if not zonePlayers then return end
  for team, players in pairs(zonePlayers) do
    for playerId, _ in pairs(players) do
      local id = tonumber(playerId)
      local pdata = playerData[id]
      if pdata then
        local oldLevel = pdata.level or 0
        -- Award XP and money
        pdata.money = (pdata.money or 0) + bonusMoney
        pdata.xp = (pdata.xp or 0) + bonusXP
        -- Recalculate level
        pdata.level = CalculateLevel(pdata.xp)
        -- Trigger level up event if applicable
        if pdata.level > oldLevel then
          TriggerClientEvent('koth:levelUp', id, { newLevel = pdata.level, oldLevel = oldLevel })
        end
        -- Immediately update the client HUD with new stats
        TriggerClientEvent('koth:updatePlayerData', id, pdata)
        -- Persist to database asynchronously
        SavePlayerData(id)
      end
    end
  end
end

-- Track per-player owned weapons in memory.  Keyed by server ID and
-- class name, this table stores a set of weapon hashes that the
-- player has purchased permanently.  Owned weapons may be spawned
-- for free and bypass class-level purchase restrictions.  Data is
-- loaded from the database when players join and updated when they
-- purchase a weapon.
local playerOwnedWeapons = {}

-- Prestige system data.  Each playerData entry will have the following
-- additional fields:
--   prestigeRank           Number of times the player has prestiged (0-5)
--   prestigeWeaponTokens   Tokens available to unlock prestige weapons
--   prestigeVehicleTokens  Tokens available to unlock prestige vehicles
-- Tokens are awarded when a player reaches level 50 and chooses to
-- prestige via the prestige NPC.  These values persist across sessions
-- and should be saved to the database along with money/xp/level.

--
-- Award a victory bonus to all players on the specified team at the
-- end of a round.  When a team wins (either by reaching the point
-- threshold or via an admin-forced switch), each member of that
-- team receives a lump sum of XP and money.  These values are
-- configurable here.  The function iterates over the playerTeams
-- table, updates the relevant playerData entry, recalculates their
-- level and persists the data.  It also triggers client events to
-- immediately reflect the updated stats on the HUD.
--
-- Parameters:
--   team (string): 'red', 'green' or 'blue' identifying the winning team.
local function AwardRoundWin(team)
  if not team then return end
  -- Define the bonus amounts for a round victory
  local bonusXP = 250
  local bonusMoney = 2000
  -- Iterate over all players and award those on the winning team
  for playerId, playerTeam in pairs(playerTeams) do
    if playerTeam == team then
      local pdata = playerData[tonumber(playerId)]
      if pdata then
        local oldLevel = pdata.level or 0
        pdata.money = (pdata.money or 0) + bonusMoney
        pdata.xp = (pdata.xp or 0) + bonusXP
        pdata.level = CalculateLevel(pdata.xp)
        -- Notify of level up if applicable
        if pdata.level > oldLevel then
          TriggerClientEvent('koth:levelUp', tonumber(playerId), { newLevel = pdata.level, oldLevel = oldLevel })
        end
        -- Inform client of updated stats
        TriggerClientEvent('koth:updatePlayerData', tonumber(playerId), pdata)
        -- Persist changes
        SavePlayerData(tonumber(playerId))
      end
    end
  end
  -- Broadcast a message to all players announcing the rewards
  TriggerClientEvent('chat:addMessage', -1, {
    color = {255, 215, 0},
    multiline = true,
    args = { '[KOTH]', string.format('The %s team has won the round and received $%d and %d XP!', team:upper(), bonusMoney, bonusXP) }
  })
end

-- Initialise the owned weapons table in the database.  This table
-- records which weapons each player has purchased for each class.
-- The combination of txid (license identifier), class and weapon
-- serves as the primary key to avoid duplicates.
MySQL.ready(function()
  MySQL.Async.execute([[CREATE TABLE IF NOT EXISTS koth_player_owned_weapons (
      txid   VARCHAR(50) NOT NULL,
      class  VARCHAR(20) NOT NULL,
      weapon VARCHAR(50) NOT NULL,
      PRIMARY KEY (txid, class, weapon)
  )]], {}, function(rowsChanged)
    print('[KOTH] Database initialised - owned weapons table ready')
  end)
end)

-- Flag indicating whether a round is currently ending.  When a team hits
-- POINTS_TO_WIN, this flag is set to true to prevent additional point
-- awards and multiple round-end triggers.  It is reset to false when
-- RotateMap() completes.
local isRoundEnding = false

-- How many points a team needs to win the round.  It takes roughly one
-- second to award a point when a team is controlling the KOTH zone.  Set
-- this lower for faster testing or higher for longer matches.  The default
-- behaviour requested by the server owner is 150 points to win, but for
-- testing we can override this value (e.g. to 60) so that a round ends in
-- about a minute.  Feel free to adjust this constant as needed.
-- Number of zone points a team must earn to win the round.  Setting this
-- low (e.g., 10) allows you to quickly test the map-rotation feature.
-- The number of points a team must earn to win the round.  Previously set
-- to 10 for rapid testing, this has been increased to 100 to lengthen
-- rounds.  Teams now need to reach 100 points to win.
local POINTS_TO_WIN = 100

-- Round duration in milliseconds.  A round will automatically end
-- when this time limit is reached, regardless of which team is
-- leading.  Set to 20 minutes (20 * 60 * 1000).  When adding new
-- maps, the rotation system will respect this duration as well.
local ROUND_DURATION_MS = 20 * 60 * 1000

-- Timestamp (GetGameTimer) marking when the current round began.  This
-- is set in RotateMap() and used by the round timer thread.
local roundStartTime = nil

-- Define a set of possible map configurations for KOTH rotation.  Each
-- configuration specifies the KOTH zone center and radius, along with spawn
-- positions for each team.  When a team reaches the point limit, the next
-- map in this list will be selected.  Feel free to adjust these coordinates
-- to suit your server's map.  Coordinates were chosen to be spread across
-- San Andreas for varied PvP.
-- Map rotation definitions.
--
-- The first entry remains the original quarry map supplied with this
-- resource.  The subsequent entries correspond to the new KOTH zones
-- requested by the server owner.  For each additional zone we compute
-- three spawn positions roughly one kilometre away from the capture
-- area.  Spawns are offset along the cardinal axes to ensure that
-- teams start a safe distance from the objective.  Red spawns 1000
-- metres to the east of the zone centre (facing south), blue spawns
-- 1000 metres to the west (facing north) and green spawns 1000
-- metres to the north (facing east).  All zones use a radius of
-- 200.0 by default.
--
-- Define the rotation list of KOTH maps.  This table now contains all
-- twenty‑one custom maps supplied by the server owner instead of the
-- previous five placeholder zones.  Each entry defines the capture
-- zone (centre and radius) along with explicit spawn positions for
-- the red, blue and green teams.  When a spawn location was absent
-- from the original map definition, it has been generated by
-- offsetting 1000 units from the zone centre in the appropriate
-- cardinal direction (east for red, west for blue and north for green),
-- and the heading is calculated to face back toward the objective.
local mapLocations = {
  -- 1 (Church)
  {
    zone = { x = -320.0049, y = 2805.7620, z = 56.8877, radius = 225.0 },
    spawns = {
      red = { x = -1592.3440, y = 2931.1220, z = 32.8645, heading = 84.373 },
      blue = { x = -189.1411, y = 1923.3350, z = 197.6967, heading = 188.435 },
      green = { x = 1634.1590, y = 3139.5980, z = 43.3009, heading = 279.694 },
    }
  },
  -- 2 (Construction)
  {
    zone = { x = 1125.6500, y = 2381.3900, z = 49.8810, radius = 300.0 },
    spawns = {
      red = { x = 2786.9000, y = 3468.6700, z = 54.7900, heading = 303.205 },
      blue = { x = 696.2000, y = 634.1400, z = 128.9100, heading = 166.191 },
      green = { x = -1144.3000, y = 2666.8400, z = 18.0900, heading = 82.833 },
    }
  },
  -- 3 (Granny's House)
  {
    zone = { x = 2264.0000, y = 4922.0000, z = 41.0000, radius = 350.0 },
    spawns = {
      red = { x = 705.2200, y = 4182.5700, z = 40.1800, heading = 115.378 },
      blue = { x = 1582.2470, y = 6436.1450, z = 24.9178, heading = 24.240 },
      green = { x = 2674.2300, y = 3260.4800, z = 55.2400, heading = 193.869 },
    }
  },
  -- 4 (Grapeseed)
  {
    zone = { x = 1921.3330, y = 4850.7470, z = 47.0626, radius = 300.0 },
    spawns = {
      red = { x = 770.4169, y = 4261.8369, z = 56.2856, heading = 117.098 },
      blue = { x = 1565.0159, y = 6460.6851, z = 24.0606, heading = 12.480 },
      green = { x = 2786.8400, y = 3468.6700, z = 54.7900, heading = 212.056 },
    }
  },
  -- 5 (Industrial)
  {
    zone = { x = 931.7300, y = -2082.4200, z = 30.5700, radius = 250.0 },
    spawns = {
      red = { x = 1153.9900, y = -843.3500, z = 54.5000, heading = 349.831 },
      blue = { x = -338.8800, y = -1483.2700, z = 30.5900, heading = 64.754 },
      green = { x = 1107.2300, y = -3132.1300, z = 5.9000, heading = 189.491 },
    }
  },
  -- 6 (La Mesa)
  {
    zone = { x = 792.5750, y = -1193.1870, z = 45.3660, radius = 250.0 },
    spawns = {
      red = { x = 1063.8100, y = -2173.4300, z = 31.8700, heading = 195.467 },
      blue = { x = 288.3400, y = -340.9400, z = 44.9200, heading = 30.611 },
      green = { x = 792.5750, y = -193.1870, z = 45.3660, heading = 0.000 },
    }
  },
  -- 7 (Legion Square)
  {
    zone = { x = 181.3700, y = -968.9500, z = 29.5800, radius = 225.0 },
    spawns = {
      red = { x = 696.1500, y = 634.1400, z = 128.9100, heading = 342.197 },
      blue = { x = -1598.7300, y = -920.5500, z = 8.9800, heading = 88.443 },
      green = { x = 1068.7400, y = -2181.7500, z = 31.5400, heading = 216.192 },
    }
  },
  -- 8 (Little Seoul)
  {
    zone = { x = -637.8720, y = -957.5600, z = 21.4950, radius = 225.0 },
    spawns = {
      red = { x = -1645.3060, y = -192.0920, z = 55.2790, heading = 52.772 },
      blue = { x = 427.5300, y = -981.8770, z = 30.6950, heading = 268.692 },
      green = { x = -1054.6420, y = -2016.0400, z = 13.1550, heading = 158.508 },
    }
  },
  -- 9 (Lumber Yard)
  {
    zone = { x = -603.4800, y = 5365.0900, z = 71.3000, radius = 300.0 },
    spawns = {
      red = { x = 995.5120, y = 6488.1890, z = 20.9730, heading = 305.083 },
      blue = { x = -2203.1210, y = 4253.0770, z = 47.5620, heading = 124.806 },
      green = { x = -603.4800, y = 6365.0900, z = 71.3000, heading = 0.000 },
    }
  },
  -- 10 (Mirror Park)
  {
    zone = { x = 1114.9900, y = -549.0700, z = 58.0900, radius = 245.0 },
    spawns = {
      red = { x = 1063.8100, y = -2173.4300, z = 31.8700, heading = 178.195 },
      blue = { x = 696.1500, y = 634.1400, z = 128.9100, heading = 19.493 },
      green = { x = -464.1300, y = -625.2500, z = 31.1700, heading = 92.762 },
    }
  },
  -- 11 (Movie Set)
  {
    zone = { x = -1093.9650, y = -395.3930, z = 38.2950, radius = 250.0 },
    spawns = {
      red = { x = -1820.7300, y = 786.3700, z = 138.0300, heading = 31.591 },
      blue = { x = 237.9430, y = -872.8480, z = 30.4760, heading = 250.279 },
      green = { x = -1093.9650, y = 604.6070, z = 38.2950, heading = 0.000 },
    }
  },
  -- 12 (Oilfields)
  {
    zone = { x = 1634.3630, y = -1661.8042, z = 111.3187, radius = 300.0 },
    spawns = {
      red = { x = 2782.1121, y = -712.0995, z = 5.6587, heading = 309.606 },
      blue = { x = 202.8054, y = -1286.2861, z = 29.1728, heading = 75.302 },
      green = { x = -248.6776, y = -2659.8430, z = 6.5003, heading = 117.924 },
    }
  },
  -- 13 (Pacific Standard)
  {
    zone = { x = 181.9780, y = 32.6970, z = 73.4400, radius = 260.0 },
    spawns = {
      red = { x = -1292.3260, y = 258.8540, z = 63.4480, heading = 81.279 },
      blue = { x = -338.8800, y = -1483.2700, z = 30.5900, heading = 161.038 },
      green = { x = 1356.4160, y = 1209.4570, z = 109.4820, heading = 315.057 },
    }
  },
  -- 14 (Paleto)
  {
    zone = { x = -238.6950, y = 6295.6090, z = 31.5040, radius = 400.0 },
    spawns = {
      red = { x = -1558.2330, y = 4970.7560, z = 61.8670, heading = 135.115 },
      blue = { x = 1562.7960, y = 6456.8960, z = 23.8540, heading = 275.116 },
      green = { x = -238.6950, y = 7295.6090, z = 31.5040, heading = 0.000 },
    }
  },
  -- 15 (Prison)
  {
    zone = { x = 1689.3270, y = 2605.7210, z = 45.5650, radius = 250.0 },
    spawns = {
      red = { x = 2835.1800, y = 4779.6600, z = 48.9200, heading = 332.207 },
      blue = { x = 696.1500, y = 634.1400, z = 128.9100, heading = 153.263 },
      green = { x = -485.1830, y = 2824.4160, z = 35.5370, heading = 84.257 },
    }
  },
  -- 16 (Sandy Shores)
  {
    zone = { x = 1767.1530, y = 3729.9150, z = 34.0120, radius = 400.0 },
    spawns = {
      red = { x = 150.1870, y = 3144.9990, z = 42.9340, heading = 109.887 },
      blue = { x = 2835.1800, y = 4779.6600, z = 48.9200, heading = 314.505 },
      green = { x = 2532.9000, y = 2606.3500, z = 37.9400, heading = 214.276 },
    }
  },
  -- 17 (Scrapyard)
  {
    zone = { x = -458.5110, y = -1610.4170, z = 39.1420, radius = 200.0 },
    spawns = {
      red = { x = 288.3400, y = -340.9400, z = 44.9200, heading = 329.531 },
      blue = { x = -1598.7300, y = -920.5500, z = 8.9800, heading = 58.825 },
      green = { x = 1068.7400, y = -2181.7500, z = 31.5400, heading = 249.490 },
    }
  },
  -- 18 (Southside)
  {
    zone = { x = 143.0000, y = -1743.4000, z = 29.1000, radius = 275.0 },
    spawns = {
      red = { x = 288.3400, y = -340.9400, z = 44.9200, heading = 354.083 },
      blue = { x = -815.8400, y = -1092.7300, z = 10.9300, heading = 55.839 },
      green = { x = -880.5000, y = -2585.5200, z = 13.8300, heading = 129.447 },
    }
  },
  -- 19 (TownHall)
  {
    zone = { x = -624.8600, y = -184.5000, z = 37.7600, radius = 270.0 },
    spawns = {
      red = { x = -1820.7300, y = 786.3700, z = 138.0300, heading = 50.928 },
      blue = { x = -338.8800, y = -1483.2700, z = 30.5900, heading = 192.418 },
      green = { x = 696.1500, y = 634.1400, z = 128.9100, heading = 301.787 },
    }
  },
  -- 20 (University)
  {
    zone = { x = -1657.8130, y = 170.8110, z = 81.1590, radius = 300.0 },
    spawns = {
      red = { x = -657.8130, y = 170.8110, z = 81.1590, heading = 270.000 },
      blue = { x = -815.8400, y = -1092.7300, z = 10.9300, heading = 213.678 },
      green = { x = 80.4660, y = 255.0200, z = 108.7940, heading = 272.773 },
    }
  },
  -- 21 (Vespucci Canals)
  {
    zone = { x = -1034.2200, y = -1072.1600, z = 4.0900, radius = 275.0 },
    spawns = {
      red = { x = -1820.7300, y = 786.3700, z = 138.0300, heading = 22.938 },
      blue = { x = 281.7700, y = -2077.7600, z = 16.9400, heading = 232.615 },
      green = { x = -55.4600, y = -215.6600, z = 45.4400, heading = 311.189 },
    }
  }
}

--[[
  Friendly names for each map in the same order as mapLocations above.
  These names are broadcast to clients during map votes so players can
  identify the choices.  If you add or remove map configurations from
  mapLocations you must update this list to remain in sync.  The
  entries correspond to the maps extracted from the user's uploaded
  files: church, construction, granny's house, grapeseed, industrial,
  la mesa, legion square, little seoul, lumber yard, mirror park,
  movie set, oilfields, pacific standard, paleto, prison, sandy
  shores, scrapyard, southside, townhall, university and vespucci
  canals.
]]
local mapNames = {
  "Church",
  "Construction",
  "Granny's House",
  "Grapeseed",
  "Industrial",
  "La Mesa",
  "Legion Square",
  "Little Seoul",
  "Lumber Yard",
  "Mirror Park",
  "Movie Set",
  "Oilfields",
  "Pacific Standard",
  "Paleto",
  "Prison",
  "Sandy Shores",
  "Scrapyard",
  "Southside",
  "TownHall",
  "University",
  "Vespucci Canals"
}

--[[
  Map voting state.  When a round ends the server will initiate a
  voting session instead of immediately rotating to the next map.  The
  voting period allows players to choose which map to play next.  See
  StartMapVote() below for implementation details.
]]
local isVotingActive = false
local voteCounts = {}
local playersVoted = {}
-- Table of map indices that are currently eligible for voting.  When a vote
-- begins the server chooses a small subset of maps from mapLocations and
-- populates this table.  Votes for maps outside this list will be
-- ignored.  Reset when a vote ends.
local voteCandidates = {}
-- Duration of the vote in milliseconds (30 seconds by default).
-- Duration of the map vote in milliseconds.  Reduce to 15 seconds
-- (15000 ms) so that voting concludes more quickly.  This value
-- controls how long players have to pick one of the three random
-- maps before the vote ends and the winner is selected.
local VOTE_DURATION_MS = 15000

--[[
  Rotate to a specific map index.  This helper performs the same work
  as RotateMap() but takes an explicit index rather than advancing
  sequentially.  It is used by the map voting system to load the map
  that received the most votes.  If the index is out of bounds it
  falls back to 1.

  Parameters:
    index (number): The 1‑based index of the map in mapLocations to
                    load.  Values outside the range of mapLocations
                    will be clamped.
]]
local function RotateToSpecificMap(index)
  -- Clamp index into valid range
  if type(index) ~= 'number' then index = 1 end
  if index < 1 then index = 1 end
  if index > #mapLocations then index = 1 end
  currentMapIndex = index
  local newMap = mapLocations[currentMapIndex]

  -- Predefined spawns for this map
  local spawns = newMap.spawns

  -- Reset zone points and per‑round state
  zonePoints.red, zonePoints.blue, zonePoints.green = 0, 0, 0
  roundKills = {}
  kothZone.controllingTeam = nil
  kothZone.captureProgress = 0
  kothZone.dominantTeam = nil
  kothZone.isContested = false
  kothZone.playersInZone.red = 0
  kothZone.playersInZone.green = 0
  kothZone.playersInZone.blue = 0

  -- Update team spawn points
  teamSpawns = {
    red = spawns.red,
    blue = spawns.blue,
    green = spawns.green
  }

  -- Inform clients of reset zone points
  TriggerClientEvent('koth:updateZonePoints', -1, zonePoints)

  -- Reset team assignments and counts for the new round
  playerTeams = {}
  teamCounts = { red = 0, blue = 0, green = 0 }

  -- Notify clients to reload the map with the new zone/spawn data
  TriggerClientEvent('koth:rotateMap', -1, {
    zone = newMap.zone,
    spawns = spawns,
    mapIndex = currentMapIndex
  })

  print(('[KOTH] Rotated to voted map #%d (%s)'):format(currentMapIndex, mapNames[currentMapIndex] or 'Unknown'))

  -- End of map rotation; allow scoring again
  isRoundEnding = false

  -- Record start time for the next round and inform clients
  roundStartTime = GetGameTimer()
  TriggerClientEvent('koth:roundStarted', -1, {
    startTime = roundStartTime,
    duration = ROUND_DURATION_MS
  })
end

--[[
  Initiate a map vote at the end of a round.  This function
  broadcasts the list of map names to all clients and starts a timer
  for VOTE_DURATION_MS.  Players cast their votes via the
  'koth:submitVote' event.  When the timer expires the map with the
  highest vote count is loaded via RotateToSpecificMap().
]]
function StartMapVote()
  -- Do not start another vote if one is active
  if isVotingActive then
    return
  end
  isVotingActive = true
  voteCounts = {}
  playersVoted = {}
  voteCandidates = {}
  -- Select up to three random maps (excluding the current map) for the
  -- vote.  Build a list of available indices first.
  local available = {}
  for i = 1, #mapLocations do
    if i ~= currentMapIndex then
      table.insert(available, i)
    end
  end
  -- If fewer than three maps are available just use all of them.
  local candidateCount = math.min(3, #available)
  for j = 1, candidateCount do
    local idx = math.random(1, #available)
    local selected = available[idx]
    table.insert(voteCandidates, selected)
    table.remove(available, idx)
  end
  -- Initialise vote counts for chosen candidates
  for _, idx in ipairs(voteCandidates) do
    voteCounts[idx] = 0
  end
  print('[KOTH] Starting map vote')
  -- Build options array with names and indices to send to clients
  local options = {}
  local namesList = {}
  for _, idx in ipairs(voteCandidates) do
    local fname = mapNames[idx] or ('Map #' .. idx)
    table.insert(options, { index = idx, name = fname })
    table.insert(namesList, fname)
  end
  -- Notify clients to display the voting interface with only these options.
  -- Include a `names` array for backward compatibility with the legacy
  -- keyboard-based voting UI.  Clients that only handle `names` will
  -- still display a list of the selected maps.
  TriggerClientEvent('koth:startMapVote', -1, {
    options = options,
    names = namesList,
    duration = VOTE_DURATION_MS
  })
  -- Set a timeout to end the vote after the configured duration
  SetTimeout(VOTE_DURATION_MS, function()
    -- Determine the winning map
    local highest = -1
    local winners = {}
    for _, idx in ipairs(voteCandidates) do
      local count = voteCounts[idx] or 0
      if count > highest then
        highest = count
        winners = { idx }
      elseif count == highest then
        table.insert(winners, idx)
      end
    end
    -- If no votes cast, choose a random map from the candidates
    local chosenIndex
    if highest <= 0 then
      chosenIndex = voteCandidates[math.random(1, #voteCandidates)]
      print('[KOTH] No votes cast, selecting random map from candidates')
    else
      -- Randomly select among tied winners
      chosenIndex = winners[math.random(1, #winners)]
    end
    print(('[KOTH] Map vote concluded. Winner: #%d (%s) with %d votes'):format(chosenIndex, mapNames[chosenIndex] or 'Unknown', voteCounts[chosenIndex] or 0))
    -- Inform clients of the vote results
    TriggerClientEvent('koth:endMapVote', -1, {
      winner = chosenIndex,
      votes = voteCounts
    })
    -- Load the selected map
    RotateToSpecificMap(chosenIndex)
    isVotingActive = false
    voteCandidates = {}
  end)
end

--[[
  Register a server event to accept votes from clients.  Players send
  their chosen map index via this event.  Duplicate votes overwrite
  the player's previous vote.  Votes are ignored if no vote is
  active or if the index is invalid.
]]
RegisterNetEvent('koth:submitVote')
AddEventHandler('koth:submitVote', function(mapIndex)
  local src = source
  if not isVotingActive then
    return
  end
  local idx = tonumber(mapIndex)
  if not idx or idx < 1 or idx > #mapLocations then
    return
  end
  -- Ensure the vote is for one of the current candidates
  local allowed = false
  for _, cand in ipairs(voteCandidates) do
    if cand == idx then
      allowed = true
      break
    end
  end
  if not allowed then
    return
  end
  -- If the player has voted before, decrement their previous vote
  local prev = playersVoted[src]
  if prev and voteCounts[prev] then
    voteCounts[prev] = voteCounts[prev] - 1
    if voteCounts[prev] < 0 then voteCounts[prev] = 0 end
  end
  -- Record new vote
  playersVoted[src] = idx
  voteCounts[idx] = (voteCounts[idx] or 0) + 1
  print(('[KOTH] Player %s voted for map #%d (%s)'):format(src, idx, mapNames[idx] or 'Unknown'))

  -- Broadcast updated vote counts to all clients.  This allows the UI to
  -- display the number of votes each map has received in real time.  We
  -- send the entire voteCounts table; the client will filter out
  -- entries for maps that are not part of the current vote.
  TriggerClientEvent('koth:updateVoteCounts', -1, { counts = voteCounts })
end)

-- At resource start, ensure the initial team spawn locations reflect
-- the spawns configured for the first map.  Without this reassignment,
-- players who join before the first round ends would spawn at the
-- placeholder coordinates defined earlier.  By setting teamSpawns to
-- mapLocations[1].spawns, we guarantee that the first round uses the
-- intended spawn points.
teamSpawns = {
  red   = mapLocations[1].spawns.red,
  blue  = mapLocations[1].spawns.blue,
  green = mapLocations[1].spawns.green
}

-- Track which map is currently active.  Starts at the first map.
local currentMapIndex = 1

-- Rotate to the next map in the mapLocations list.  Resets zone points and
-- KOTH zone status, updates team spawns, and notifies clients to reload
-- their blips, peds and zone data.  Called when a team reaches the point
-- limit.
local function RotateMap()
  -- Randomly select a new map index to provide variety between rounds.
  -- If there is only one map defined this will always be 1.  Ensure the
  -- new index differs from the current one when multiple maps are
  -- available to avoid back‑to‑back repeats.
  local prevIndex = currentMapIndex
  if #mapLocations <= 1 then
    currentMapIndex = 1
  else
    -- Generate random index until it differs from previous
    local newIndex = math.random(1, #mapLocations)
    if newIndex == prevIndex then
      newIndex = (newIndex % #mapLocations) + 1
    end
    currentMapIndex = newIndex
  end
  local newMap = mapLocations[currentMapIndex]

  -- Use the predefined spawn coordinates from the map configuration rather
  -- than generating offsets from the capture zone.  These spawns have
  -- been manually chosen to avoid water and ensure players spawn on
  -- stable ground away from the KOTH zone.  If you need to tweak the
  -- positions further, edit the `spawns` tables in the mapLocations
  -- definitions at the top of this file.
  local spawns = newMap.spawns

  -- Reset zone points for the new round
  zonePoints.red, zonePoints.blue, zonePoints.green = 0, 0, 0

  -- Reset per‑round kill counts.  When a new map begins the
  -- leaderboard should start fresh.  Clearing the roundKills table
  -- here ensures that kills from the previous round do not carry
  -- over.  Clients will request the leaderboard on demand and will
  -- only see kills from the current round.
  roundKills = {}

  -- Reset KOTH zone state
  kothZone.controllingTeam = nil
  kothZone.captureProgress = 0
  kothZone.dominantTeam = nil
  kothZone.isContested = false
  kothZone.playersInZone.red = 0
  kothZone.playersInZone.green = 0
  kothZone.playersInZone.blue = 0

  -- Update team spawn table on the server.  When players select a team
  -- after rotation, they will spawn at these coordinates.  This also
  -- ensures that server-side logic uses the same spawn data as the
  -- clients.
  teamSpawns = {
    red = spawns.red,
    blue = spawns.blue,
    green = spawns.green
  }

  -- Inform clients that zone points have been reset for the new round
  TriggerClientEvent('koth:updateZonePoints', -1, zonePoints)

  -- Reset team assignments and counts at the start of a new round.  Clients
  -- will be prompted to select a team again.  Without this reset, players
  -- could be locked out of certain teams due to counts from the previous
  -- round persisting.
  playerTeams = {}
  teamCounts = { red = 0, blue = 0, green = 0 }

  -- Notify clients of the new map configuration.  Clients will update
  -- their KOTH zone position, team spawns, blips and peds, and reset
  -- their team selection state accordingly.
  TriggerClientEvent('koth:rotateMap', -1, {
    zone = newMap.zone,
    spawns = spawns,
    mapIndex = currentMapIndex
  })

  -- Prestige shop peds are now spawned as part of the team ped table on
  -- the client.  We no longer spawn a standalone prestige ped on
  -- map rotation.  The additional entry in the pedSpawns table (see
  -- client.lua) handles creating a prestige ped at each team base.

  print(('[KOTH] Rotated to new map #%d'):format(currentMapIndex))

  -- Round has finished rotating; scoring can resume for the next map
  isRoundEnding = false

  -- Record the start time of the new round and inform clients.  The
  -- GetGameTimer native returns milliseconds since game start.  Use
  -- this value to calculate elapsed time on both server and client.
  roundStartTime = GetGameTimer()
  TriggerClientEvent('koth:roundStarted', -1, {
    startTime = roundStartTime,
    duration = ROUND_DURATION_MS
  })
end

-- Background thread to monitor round duration.  If the configured
-- duration elapses without a team reaching the points threshold, the
-- round ends automatically and the map rotates.  This ensures that
-- rounds do not last indefinitely when teams are evenly matched or
-- fail to capture points.  The thread checks once per second to
-- minimise overhead.
Citizen.CreateThread(function()
  while true do
    Citizen.Wait(1000)
    if roundStartTime and not isRoundEnding then
      local elapsed = GetGameTimer() - roundStartTime
      if elapsed >= ROUND_DURATION_MS then
        print('[KOTH] Round time limit reached, initiating map vote')
        -- Prevent any further scoring/rotation until voting completes
        isRoundEnding = true
        -- Notify clients that the round has ended due to the time limit.  Use
        -- a special string to indicate a timeout for the feed message.
        TriggerClientEvent('koth:roundEnd', -1, 'Time')
        -- Start the map vote after a short delay to allow the end‑round
        -- camera and scoreboard to display.  The delay should mirror the
        -- existing 10 second delay used when a team reaches the score limit.
        SetTimeout(10000, function()
          StartMapVote()
        end)
      end
    end
  end
end)

--- Attachment data with correct weapon-specific components
-- Adjusted attachment price as requested by the server owner.  All
-- attachments now cost $50 instead of the previous $150.
local attachmentPrice = 50

-- Weapon-specific attachments
local weaponAttachments = {
  -- Assault Rifles
  ["WEAPON_ASSAULTRIFLE"] = {
    { name = "Extended Magazine", component = "COMPONENT_ASSAULTRIFLE_CLIP_02", image = "attachment_extendedmag.png" },
    { name = "Drum Magazine", component = "COMPONENT_ASSAULTRIFLE_CLIP_03", image = "attachment_extendedmag2.png" },
    { name = "Suppressor", component = "COMPONENT_AT_AR_SUPP_02", image = "attachment_suppressor.png" },
    { name = "Flashlight", component = "COMPONENT_AT_AR_FLSH", image = "attachment_flashlight.png" },
    { name = "Grip", component = "COMPONENT_AT_AR_AFGRIP", image = "attachment_grip.png" },
    { name = "Scope", component = "COMPONENT_AT_SCOPE_MACRO", image = "attachment_scope.png" }
  },
  ["WEAPON_CARBINERIFLE"] = {
    { name = "Extended Magazine", component = "COMPONENT_CARBINERIFLE_CLIP_02", image = "attachment_extendedmag.png" },
    { name = "Box Magazine", component = "COMPONENT_CARBINERIFLE_CLIP_03", image = "attachment_extendedmag2.png" },
    { name = "Suppressor", component = "COMPONENT_AT_AR_SUPP", image = "attachment_suppressor.png" },
    { name = "Flashlight", component = "COMPONENT_AT_AR_FLSH", image = "attachment_flashlight.png" },
    { name = "Grip", component = "COMPONENT_AT_AR_AFGRIP", image = "attachment_grip.png" },
    { name = "Scope", component = "COMPONENT_AT_SCOPE_MEDIUM", image = "attachment_scope.png" }
  },
  ["WEAPON_ADVANCEDRIFLE"] = {
    { name = "Extended Magazine", component = "COMPONENT_ADVANCEDRIFLE_CLIP_02", image = "attachment_extendedmag.png" },
    { name = "Suppressor", component = "COMPONENT_AT_AR_SUPP", image = "attachment_suppressor.png" },
    { name = "Flashlight", component = "COMPONENT_AT_AR_FLSH", image = "attachment_flashlight.png" },
    { name = "Scope", component = "COMPONENT_AT_SCOPE_SMALL", image = "attachment_scope.png" }
  },
  ["WEAPON_SPECIALCARBINE"] = {
    { name = "Extended Magazine", component = "COMPONENT_SPECIALCARBINE_CLIP_02", image = "attachment_extendedmag.png" },
    { name = "Drum Magazine", component = "COMPONENT_SPECIALCARBINE_CLIP_03", image = "attachment_extendedmag2.png" },
    { name = "Suppressor", component = "COMPONENT_AT_AR_SUPP_02", image = "attachment_suppressor.png" },
    { name = "Flashlight", component = "COMPONENT_AT_AR_FLSH", image = "attachment_flashlight.png" },
    { name = "Grip", component = "COMPONENT_AT_AR_AFGRIP", image = "attachment_grip.png" },
    { name = "Scope", component = "COMPONENT_AT_SCOPE_MEDIUM", image = "attachment_scope.png" }
  },
  ["WEAPON_BULLPUPRIFLE"] = {
    { name = "Extended Magazine", component = "COMPONENT_BULLPUPRIFLE_CLIP_02", image = "attachment_extendedmag.png" },
    { name = "Suppressor", component = "COMPONENT_AT_AR_SUPP", image = "attachment_suppressor.png" },
    { name = "Flashlight", component = "COMPONENT_AT_AR_FLSH", image = "attachment_flashlight.png" },
    { name = "Grip", component = "COMPONENT_AT_AR_AFGRIP", image = "attachment_grip.png" },
    { name = "Scope", component = "COMPONENT_AT_SCOPE_SMALL", image = "attachment_scope.png" }
  },
  -- SMGs
  ["WEAPON_SMG"] = {
    { name = "Extended Magazine", component = "COMPONENT_SMG_CLIP_02", image = "attachment_extendedmag.png" },
    { name = "Drum Magazine", component = "COMPONENT_SMG_CLIP_03", image = "attachment_extendedmag2.png" },
    { name = "Suppressor", component = "COMPONENT_AT_PI_SUPP", image = "attachment_suppressor.png" },
    { name = "Flashlight", component = "COMPONENT_AT_AR_FLSH", image = "attachment_flashlight.png" },
    { name = "Scope", component = "COMPONENT_AT_SCOPE_MACRO_02", image = "attachment_scope.png" }
  },
  ["WEAPON_MICROSMG"] = {
    { name = "Extended Magazine", component = "COMPONENT_MICROSMG_CLIP_02", image = "attachment_extendedmag.png" },
    { name = "Suppressor", component = "COMPONENT_AT_AR_SUPP_02", image = "attachment_suppressor.png" },
    { name = "Flashlight", component = "COMPONENT_AT_PI_FLSH", image = "attachment_flashlight.png" },
    { name = "Scope", component = "COMPONENT_AT_SCOPE_MACRO", image = "attachment_scope.png" }
  },
  ["WEAPON_ASSAULTSMG"] = {
    { name = "Extended Magazine", component = "COMPONENT_ASSAULTSMG_CLIP_02", image = "attachment_extendedmag.png" },
    { name = "Suppressor", component = "COMPONENT_AT_AR_SUPP_02", image = "attachment_suppressor.png" },
    { name = "Flashlight", component = "COMPONENT_AT_AR_FLSH", image = "attachment_flashlight.png" },
    { name = "Scope", component = "COMPONENT_AT_SCOPE_MACRO", image = "attachment_scope.png" }
  },
  ["WEAPON_COMBATPDW"] = {
    { name = "Extended Magazine", component = "COMPONENT_COMBATPDW_CLIP_02", image = "attachment_extendedmag.png" },
    { name = "Drum Magazine", component = "COMPONENT_COMBATPDW_CLIP_03", image = "attachment_extendedmag2.png" },
    { name = "Flashlight", component = "COMPONENT_AT_AR_FLSH", image = "attachment_flashlight.png" },
    { name = "Grip", component = "COMPONENT_AT_AR_AFGRIP", image = "attachment_grip.png" },
    { name = "Scope", component = "COMPONENT_AT_SCOPE_SMALL", image = "attachment_scope.png" }
  },
  -- Pistols
  ["WEAPON_PISTOL"] = {
    { name = "Extended Magazine", component = "COMPONENT_PISTOL_CLIP_02", image = "attachment_extendedmag.png" },
    { name = "Suppressor", component = "COMPONENT_AT_PI_SUPP_02", image = "attachment_suppressor.png" },
    { name = "Flashlight", component = "COMPONENT_AT_PI_FLSH", image = "attachment_flashlight.png" }
  },
  ["WEAPON_COMBATPISTOL"] = {
    { name = "Extended Magazine", component = "COMPONENT_COMBATPISTOL_CLIP_02", image = "attachment_extendedmag.png" },
    { name = "Suppressor", component = "COMPONENT_AT_PI_SUPP", image = "attachment_suppressor.png" },
    { name = "Flashlight", component = "COMPONENT_AT_PI_FLSH", image = "attachment_flashlight.png" }
  },
  ["WEAPON_APPISTOL"] = {
    { name = "Extended Magazine", component = "COMPONENT_APPISTOL_CLIP_02", image = "attachment_extendedmag.png" },
    { name = "Suppressor", component = "COMPONENT_AT_PI_SUPP", image = "attachment_suppressor.png" },
    { name = "Flashlight", component = "COMPONENT_AT_PI_FLSH", image = "attachment_flashlight.png" }
  },
  ["WEAPON_PISTOL50"] = {
    { name = "Extended Magazine", component = "COMPONENT_PISTOL50_CLIP_02", image = "attachment_extendedmag.png" },
    { name = "Suppressor", component = "COMPONENT_AT_AR_SUPP_02", image = "attachment_suppressor.png" },
    { name = "Flashlight", component = "COMPONENT_AT_PI_FLSH", image = "attachment_flashlight.png" }
  },
  -- MGs
  ["WEAPON_MG"] = {
    { name = "Extended Magazine", component = "COMPONENT_MG_CLIP_02", image = "attachment_extendedmag.png" },
    { name = "Scope", component = "COMPONENT_AT_SCOPE_SMALL_02", image = "attachment_scope.png" }
  },
  ["WEAPON_COMBATMG"] = {
    { name = "Extended Magazine", component = "COMPONENT_COMBATMG_CLIP_02", image = "attachment_extendedmag.png" },
    { name = "Grip", component = "COMPONENT_AT_AR_AFGRIP", image = "attachment_grip.png" },
    { name = "Scope", component = "COMPONENT_AT_SCOPE_MEDIUM", image = "attachment_scope.png" }
  },
  -- Snipers
  ["WEAPON_SNIPERRIFLE"] = {
    { name = "Suppressor", component = "COMPONENT_AT_AR_SUPP_02", image = "attachment_suppressor.png" },
    { name = "Advanced Scope", component = "COMPONENT_AT_SCOPE_MAX", image = "attachment_scope2.png" }
  },
  ["WEAPON_HEAVYSNIPER"] = {
    { name = "Advanced Scope", component = "COMPONENT_AT_SCOPE_MAX", image = "attachment_scope2.png" }
  },
  ["WEAPON_MARKSMANRIFLE"] = {
    { name = "Extended Magazine", component = "COMPONENT_MARKSMANRIFLE_CLIP_02", image = "attachment_extendedmag.png" },
    { name = "Suppressor", component = "COMPONENT_AT_AR_SUPP", image = "attachment_suppressor.png" },
    { name = "Flashlight", component = "COMPONENT_AT_AR_FLSH", image = "attachment_flashlight.png" },
    { name = "Grip", component = "COMPONENT_AT_AR_AFGRIP", image = "attachment_grip.png" }
  }
}

-- Default attachments for weapons not in the list
local defaultAttachments = {
  { name = "Extended Magazine", component = "COMPONENT_AT_CLIP_02", image = "attachment_extendedmag.png" },
  { name = "Suppressor", component = "COMPONENT_AT_AR_SUPP", image = "attachment_suppressor.png" },
  { name = "Flashlight", component = "COMPONENT_AT_AR_FLSH", image = "attachment_flashlight.png" },
  { name = "Grip", component = "COMPONENT_AT_AR_AFGRIP", image = "attachment_grip.png" },
  { name = "Scope", component = "COMPONENT_AT_SCOPE_MACRO", image = "attachment_scope.png" }
}

-- Initialize team counts on resource start
AddEventHandler('onResourceStart', function(resourceName)
  if GetCurrentResourceName() ~= resourceName then return end
  
  -- Ensure team counts are initialized
  teamCounts = { red = 0, blue = 0, green = 0 }
  print('[KOTH] Team counts initialized on resource start')
end)

-- Function to update team counts and broadcast to all clients
function UpdateTeamCounts()
  -- Count actual players in each team
  local actualCounts = { red = 0, blue = 0, green = 0 }

  print('[KOTH] UpdateTeamCounts called - Current playerTeams:')
  for playerId, team in pairs(playerTeams) do
    local ping = GetPlayerPing(playerId)
    print(('  Player %d: Team %s, Ping: %d'):format(playerId, team, ping))
    if ping > 0 then -- Player is still connected
      actualCounts[team] = (actualCounts[team] or 0) + 1
      print(('    -> Counted for team %s (new count: %d)'):format(team, actualCounts[team]))
    else
      print(('    -> Skipped (no ping)'):format())
    end
  end

  -- Update global team counts
  teamCounts = actualCounts

  print(('[KOTH] Final team counts - Red: %d, Blue: %d, Green: %d'):format(
    teamCounts.red, teamCounts.blue, teamCounts.green
  ))

  -- Broadcast to all clients for HUD update
  TriggerClientEvent('koth:updateTeamCounts', -1, teamCounts)
  print('[KOTH] Broadcasted team counts to all clients')

  -- Also broadcast the current player-to-team mapping so clients can
  -- update teammate blips.  Sending the full playerTeams table to
  -- clients allows each client to determine who is on their team and
  -- create/remove blips accordingly.  We send this mapping anytime
  -- team counts change to ensure clients stay in sync even when
  -- players disconnect or switch teams.
  TriggerClientEvent('koth:updateTeamMembers', -1, playerTeams)
  print('[KOTH] Broadcasted team member list to all clients')
end

-- Function to get player's team
function GetPlayerTeam(playerId)
  return playerTeams[playerId]
end

-- Helper to load a player's owned weapons into memory.  If `classId` is
-- provided, only weapons for that class will be fetched; otherwise all
-- owned weapon records are retrieved.  This function uses the
-- player's licence (txid) to query the database and populates the
-- `playerOwnedWeapons` table for quick lookups.  It is safe to call
-- this multiple times; existing entries will be overwritten.
local function loadOwnedWeapons(source, classId)
  local identifiers = GetPlayerIdentifiers(source)
  local txid
  for _, id in ipairs(identifiers) do
    if string.find(id, 'license:') then
      txid = id
      break
    end
  end
  if not txid then return end
  if not playerOwnedWeapons[source] then playerOwnedWeapons[source] = {} end
  -- Build query and parameters
  local query = 'SELECT class, weapon FROM koth_player_owned_weapons WHERE txid = @txid'
  local params = { ['@txid'] = txid }
  if classId then
    query = query .. ' AND class = @class'
    params['@class'] = classId
  end
  local rows = MySQL.Sync.fetchAll(query, params) or {}
  -- Reset table for the player (and class if specified)
  if classId then
    playerOwnedWeapons[source][classId] = {}
  else
    playerOwnedWeapons[source] = {}
  end
  for _, row in ipairs(rows) do
    local cls = row.class
    local weap = row.weapon
    if not playerOwnedWeapons[source][cls] then playerOwnedWeapons[source][cls] = {} end
    playerOwnedWeapons[source][cls][weap] = true
  end
end

-- Function to update zone points and broadcast to all clients
function UpdateZonePoints()
  -- Broadcast zone points to all clients
  TriggerClientEvent('koth:updateZonePoints', -1, zonePoints)

  print(('[KOTH] Zone points updated - Red: %d, Green: %d, Blue: %d'):format(
    zonePoints.red, zonePoints.green, zonePoints.blue
  ))
end

-- Function to award zone points to a team
function AwardZonePoints(team, points)
  if team and zonePoints[team] then
    zonePoints[team] = zonePoints[team] + points
    UpdateZonePoints()

    print(('[KOTH] Awarded %d points to %s team (Total: %d)'):format(
      points, team, zonePoints[team]
    ))
  end
end

-- Team selection handler (no persistence)
RegisterNetEvent('koth:pickTeam', function(team)
  local src = source
  print(('[KOTH] Player %d selected team: %s'):format(src, team or 'none'))

  -- Enforce team balancing: players must join the team with the fewest
  -- members when teams are uneven.  Determine current team counts.
  local smallestTeam, smallestCount = nil, math.huge
  for t, count in pairs(teamCounts) do
    if count < smallestCount then
      smallestCount = count
      smallestTeam = t
    end
  end
  -- If the chosen team has more players than the smallest team, reject
  -- the selection and instruct the player to join the smallest team.
  if teamCounts[team] > smallestCount then
    local msg = string.format('Teams must remain balanced. Please join the %s team.', smallestTeam)
    TriggerClientEvent('chat:addMessage', src, {
      color = {255, 0, 0},
      multiline = false,
      args = {'[KOTH]', msg}
    })
    print(('[KOTH] Denied team selection for player %d: attempted %s, recommended %s'):format(src, team, smallestTeam))
    return
  end

  local spawn = teamSpawns[team]
  if spawn then
    -- Update player's team (session only)
    playerTeams[src] = team
    print(('[KOTH] Updated playerTeams[%d] = %s'):format(src, team))

    -- Spawn player
    TriggerClientEvent('koth:spawnPlayer', src, spawn)
    print(('[KOTH] Spawning player %d at team %s'):format(src, team))

    -- Send player data immediately after team selection
    if playerData[src] then
      print(('[KOTH] Sending player data to %s after team selection'):format(GetPlayerName(src)))
      TriggerClientEvent('koth:updatePlayerData', src, playerData[src])
    else
      print(('[KOTH] Player data not loaded yet for %s, loading now...'):format(GetPlayerName(src)))
      LoadPlayerData(src)
      -- Send data after a short delay to ensure it loads
      Citizen.SetTimeout(1000, function()
        if playerData[src] then
          TriggerClientEvent('koth:updatePlayerData', src, playerData[src])
          print(('[KOTH] Sent player data to %s after loading'):format(GetPlayerName(src)))
        end
      end)
    end

    -- Update team counts for all players immediately
    print('[KOTH] Calling UpdateTeamCounts after team selection...')
    UpdateTeamCounts()
    
    -- Also send zone points immediately
    UpdateZonePoints()
    
    -- Also send a delayed update to ensure it gets through
    Citizen.SetTimeout(1000, function()
      print('[KOTH] Sending delayed team count update...')
      UpdateTeamCounts()
      -- Send player data again to ensure UI is updated
      if playerData[src] then
        TriggerClientEvent('koth:updatePlayerData', src, playerData[src])
      end
    end)
  else
    print(('[KOTH] Invalid team: %s'):format(team or 'none'))
  end
end)

-- New event to send attachment menu data to client
RegisterNetEvent('koth:getAttachmentMenu', function(weaponHash)
  local src = source
  print(('[KOTH] Attachment menu requested by player %d for weapon hash %s'):format(src, tostring(weaponHash)))

  -- Check player money
  local money = 0
  if playerData[src] and playerData[src].money then
    money = playerData[src].money
  end

  -- Get weapon name from hash
  local weaponName = GetWeaponNameFromHash(weaponHash)
  
  -- Get weapon-specific attachments.  We no longer fall back to default
  -- attachments because doing so allowed incompatible components to be
  -- purchased for many weapons.  If there are no attachments defined for
  -- the selected weapon, the returned list will be empty and the client
  -- will display "No attachments available".  This ensures that only
  -- appropriate attachments can be applied to each weapon.
  local attachments = weaponAttachments[weaponName] or {}
  
  -- Prepare attachment list with price and full image path
  local attachmentList = {}
    for _, attachment in ipairs(attachments) do
      -- Send a relative path into the HTML resource rather than using the
      -- deprecated NUI protocol.  The corresponding files live under
      -- html/images/guns with names matching the attachment definitions (e.g.
      -- attachment_extendedmag.png).  Using a relative path allows the
      -- browser to resolve the URL correctly regardless of resource name.
      table.insert(attachmentList, {
        name = attachment.name,
        component = attachment.component,
        price = attachmentPrice,
        image = "images/guns/" .. attachment.image
      })
    end

  -- Send attachment menu data to client
  TriggerClientEvent('koth:showAttachmentMenu', src, {
    attachments = attachmentList,
    weaponName = weaponName:gsub("WEAPON_", ""):gsub("_", " "),
    money = money
  })
end)

-- Helper function to get weapon name from hash
function GetWeaponNameFromHash(hash)
  -- Common weapon hash to name mapping
  local weaponHashToName = {
    [GetHashKey("WEAPON_PISTOL")] = "WEAPON_PISTOL",
    [GetHashKey("WEAPON_COMBATPISTOL")] = "WEAPON_COMBATPISTOL",
    [GetHashKey("WEAPON_APPISTOL")] = "WEAPON_APPISTOL",
    [GetHashKey("WEAPON_PISTOL50")] = "WEAPON_PISTOL50",
    [GetHashKey("WEAPON_MICROSMG")] = "WEAPON_MICROSMG",
    [GetHashKey("WEAPON_SMG")] = "WEAPON_SMG",
    [GetHashKey("WEAPON_ASSAULTSMG")] = "WEAPON_ASSAULTSMG",
    [GetHashKey("WEAPON_COMBATPDW")] = "WEAPON_COMBATPDW",
    [GetHashKey("WEAPON_ASSAULTRIFLE")] = "WEAPON_ASSAULTRIFLE",
    [GetHashKey("WEAPON_CARBINERIFLE")] = "WEAPON_CARBINERIFLE",
    [GetHashKey("WEAPON_ADVANCEDRIFLE")] = "WEAPON_ADVANCEDRIFLE",
    [GetHashKey("WEAPON_SPECIALCARBINE")] = "WEAPON_SPECIALCARBINE",
    [GetHashKey("WEAPON_BULLPUPRIFLE")] = "WEAPON_BULLPUPRIFLE",
    [GetHashKey("WEAPON_MG")] = "WEAPON_MG",
    [GetHashKey("WEAPON_COMBATMG")] = "WEAPON_COMBATMG",
    [GetHashKey("WEAPON_SNIPERRIFLE")] = "WEAPON_SNIPERRIFLE",
    [GetHashKey("WEAPON_HEAVYSNIPER")] = "WEAPON_HEAVYSNIPER",
    [GetHashKey("WEAPON_MARKSMANRIFLE")] = "WEAPON_MARKSMANRIFLE",
    -- Add more weapons as needed
  }
  
  return weaponHashToName[hash] or "WEAPON_UNKNOWN"
end

-- Event to handle attachment purchase
RegisterNetEvent('koth:purchaseAttachment', function(data)
  local src = source
  local attachmentName = data.name
  local attachmentComponent = data.component
  local price = tonumber(data.price) or attachmentPrice

  print(('[KOTH] Player %d attempting to purchase attachment %s for $%d'):format(src, attachmentName, price))

  -- Validate player money
  if not playerData[src] or not playerData[src].money then
    print(('[KOTH] Player data not loaded for %d'):format(src))
    TriggerClientEvent('koth:purchaseResult', src, false, "Player data not loaded. Please try again.")
    return
  end

  if playerData[src].money < price then
    TriggerClientEvent('koth:purchaseResult', src, false, "Not enough money to purchase attachment.")
    print(('[KOTH] Player %d has insufficient funds: $%d needed, has $%d'):format(src, price, playerData[src].money))
    return
  end

  -- Before deducting money or applying the component, verify that the
  -- requested attachment component is valid for the player's current
  -- weapon.  Pull the player's ped and currently equipped weapon, look
  -- up the corresponding attachment list and confirm the component
  -- exists.  If it does not, refuse the transaction and do not charge
  -- the player.  This prevents players from equipping attachments on
  -- weapons that do not support them.
  local ped = GetPlayerPed(src)
  local currentWeapon = GetSelectedPedWeapon(ped)
  local currentName = GetWeaponNameFromHash(currentWeapon)
  local allowed = weaponAttachments[currentName] or {}
  local isValidComponent = false
  for _, info in ipairs(allowed) do
    if info.component == attachmentComponent then
      isValidComponent = true
      break
    end
  end
  if not isValidComponent then
    -- Invalid component for this weapon: abort the purchase and inform the player
    print(('[KOTH] Player %d attempted to buy invalid component %s for weapon %s'):format(src, attachmentComponent or 'nil', currentName or 'unknown'))
    TriggerClientEvent('koth:purchaseResult', src, false, "This attachment cannot be applied to your current weapon.")
    return
  end

  -- Deduct money and persist
  playerData[src].money = playerData[src].money - price
  SavePlayerData(src)

  -- Apply attachment to player's weapon
  TriggerClientEvent('koth:applyAttachment', src, attachmentComponent)

  -- Notify purchase success
  TriggerClientEvent('koth:purchaseResult', src, true, ("Purchased %s for $%d"):format(attachmentName, price))

  -- Update player money HUD
  TriggerClientEvent('koth:updatePlayerData', src, playerData[src])
end)

-- Team count request (for team selection screen)
RegisterNetEvent('koth:requestCounts', function()
  local src = source
  print(('[KOTH] Player %d requested team counts'):format(src))

  -- Always show team selection (no persistence)
  TriggerClientEvent('koth:updateCounts', src, teamCounts)
end)

-- Vehicle ownership tracking
local playerOwnedVehicles = {} -- Track purchased vehicles per player
local playerRentedVehicles = {} -- Track rented vehicles (temporary)

--[[
  Refresh a player's class level in the local cache.  This helper
  queries the koth_classes resource to determine the player's
  currently selected class and its level, then stores the result
  directly on the playerData table under the `classLevel` key.  If
  the player has not selected a class or the external resource is not
  available, the class level defaults to 0.  Storing the class level
  on playerData ensures that when playerData is sent to clients via
  koth:updatePlayerData, the class level is included in the payload.
  @param src (number) The server ID of the player.
]]
local function refreshPlayerClassLevel(src)
  if not src then return end
  -- Default class level
  local level = 0
  -- Attempt to get player's current class from the external class
  -- system.  Use pcall to gracefully handle cases where the export
  -- might not be available (e.g. when the class resource is stopped).
  local ok, classId = pcall(function()
    return exports['koth_classes']:GetPlayerClass(src)
  end)
  if ok and classId then
    local ok2, lvl = pcall(function()
      return exports['koth_classes']:GetPlayerClassLevel(src, classId)
    end)
    if ok2 and lvl then
      level = tonumber(lvl) or 0
    end
  end
  -- Ensure playerData entry exists
  if not playerData[src] then playerData[src] = {} end
  playerData[src].classLevel = level

  -- Also update the player's class XP and the XP required for the next
  -- class level.  This allows the client to display a secondary XP
  -- progress bar for the current class.  Retrieve the player's txid
  -- from the stored player data (populated in LoadPlayerData).  If
  -- either the txid or classId is missing we skip the DB query.
  -- Fetch class XP and threshold using the exports provided by the
  -- koth_classes resource.  This ensures each class tracks XP
  -- independently rather than sharing a single XP pool.  If either
  -- export fails (e.g. the resource is stopped), default to zero.
  do
    local okXP, xpVal = pcall(function()
      return exports['koth_classes']:GetPlayerClassXP(src, classId)
    end)
    local okThr, threshold = pcall(function()
      return exports['koth_classes']:GetClassXPThreshold(level)
    end)
    playerData[src].classXP = (okXP and xpVal) or 0
    playerData[src].classMaxXP = (okThr and threshold) or 0
  end
  return level
end

-- Vehicle purchase handlers
RegisterNetEvent('koth:buyVehicle', function(data)
  local src = source
  local vehicleName = nil
  local price = nil
  
  -- Handle both old format (string) and new format (table)
  if type(data) == 'table' then
    vehicleName = data.name
    -- Defensive check for price field
    if type(data.price) == 'table' then
      print(('[KOTH] ERROR: price field is a table for player %d, vehicle %s'):format(src, vehicleName or 'none'))
      -- Try to extract price from nested table
      if data.price.price then
        price = tonumber(data.price.price) or 0
      elseif data.price.cost then
        price = tonumber(data.price.cost) or 0
      else
        price = 0
      end
    else
      price = tonumber(data.price) or tonumber(data.cost) or 0
    end
  else
    vehicleName = data
    price = 0 -- Default price if not provided
  end

  print(('[KOTH] Player %d wants to buy: %s (price: $%d)'):format(src, vehicleName or 'none', price))
  print(('[KOTH] Raw data received: %s'):format(json.encode(data)))

  if not vehicleName then
    TriggerClientEvent('koth:purchaseResult', src, false, 'Invalid vehicle name')
    return
  end

  -- Check if player has enough money
  if not playerData[src] then
    print(('[KOTH] Player data not loaded for %d, attempting to load...'):format(src))
    LoadPlayerData(src)

    Citizen.SetTimeout(500, function()
      if not playerData[src] then
        print(('[KOTH] Failed to load player data for %d after retry'):format(src))
        TriggerClientEvent('koth:purchaseResult', src, false, 'Player data not loaded. Please try again.')
      else
        print(('[KOTH] Player data loaded for %d, retrying vehicle purchase'):format(src))
        TriggerEvent('koth:buyVehicle', data)
      end
    end)
    return
  end

  -- Check if player already owns this vehicle
  if not playerOwnedVehicles[src] then
    playerOwnedVehicles[src] = {}
  end
  
  if playerOwnedVehicles[src][vehicleName] then
    -- Player already owns this vehicle, spawn it for free
    print(('[KOTH] Player %d already owns %s, spawning for free'):format(src, vehicleName))
    SpawnVehicleForPlayer(src, vehicleName, 'owned', 0)
    TriggerClientEvent('koth:purchaseResult', src, true, ('Spawned your owned %s'):format(vehicleName))
    return
  end

  -- Check money for new purchase
  if price > 0 and playerData[src].money < price then
    TriggerClientEvent('koth:purchaseResult', src, false, 'Not enough money')
    print(('[KOTH] Player %d cannot afford %s ($%d) - has $%d'):format(src, vehicleName, price, playerData[src].money))
    return
  end

  -- Deduct money and save to database
  if price > 0 then
    playerData[src].money = playerData[src].money - price
    SavePlayerData(src)
  end

  -- Add vehicle to owned list
  playerOwnedVehicles[src][vehicleName] = true

  -- Persist owned vehicle to database
  local identifiers = GetPlayerIdentifiers(src)
  if identifiers and identifiers.txid then
    exports.oxmysql:execute('INSERT IGNORE INTO koth_player_vehicles (txid, vehicle_name) VALUES (?, ?)', {identifiers.txid, vehicleName}, function(affectedRows)
      if affectedRows and affectedRows > 0 then
        print(('[KOTH] Persisted owned vehicle %s for player %d'):format(vehicleName, src))
      else
        print(('[KOTH] Owned vehicle %s for player %d already exists in database'):format(vehicleName, src))
      end
    end)
  else
    print(('[KOTH] Could not persist owned vehicle %s for player %d - missing TXID'):format(vehicleName, src))
  end
  
  print(('[KOTH] Player %d bought %s for $%d - remaining: $%d'):format(src, vehicleName, price, playerData[src].money))

  -- Update client HUD with new money
  TriggerClientEvent('koth:updatePlayerData', src, playerData[src])

  -- Spawn the vehicle
  SpawnVehicleForPlayer(src, vehicleName, 'buy', price)
  TriggerClientEvent('koth:purchaseResult', src, true, ('Purchased %s for $%d - You now own this vehicle!'):format(vehicleName, price))
end)

RegisterNetEvent('koth:rentVehicle', function(data)
  local src = source
  local vehicleName = nil
  local price = nil
  
  -- Handle both old format (string) and new format (table)
  if type(data) == 'table' then
    vehicleName = data.name
    price = tonumber(data.price) or tonumber(data.rent) or 0
  else
    vehicleName = data
    price = 0 -- Default price if not provided
  end

  print(('[KOTH] Player %d wants to rent: %s (price: $%d)'):format(src, vehicleName or 'none', price))
  print(('[KOTH] Raw data received: %s'):format(json.encode(data)))

  if not vehicleName then
    TriggerClientEvent('koth:purchaseResult', src, false, 'Invalid vehicle name')
    return
  end

  -- Check if player has enough money
  if not playerData[src] then
    print(('[KOTH] Player data not loaded for %d, attempting to load...'):format(src))
    LoadPlayerData(src)

    Citizen.SetTimeout(500, function()
      if not playerData[src] then
        print(('[KOTH] Failed to load player data for %d after retry'):format(src))
        TriggerClientEvent('koth:purchaseResult', src, false, 'Player data not loaded. Please try again.')
      else
        print(('[KOTH] Player data loaded for %d, retrying vehicle rental'):format(src))
        TriggerEvent('koth:rentVehicle', data)
      end
    end)
    return
  end

  -- Check if player already owns this vehicle
  if playerOwnedVehicles[src] and playerOwnedVehicles[src][vehicleName] then
    -- Player owns this vehicle, spawn it for free instead
    print(('[KOTH] Player %d owns %s, spawning for free instead of renting'):format(src, vehicleName))
    SpawnVehicleForPlayer(src, vehicleName, 'owned', 0)
    TriggerClientEvent('koth:purchaseResult', src, true, ('You own this vehicle! Spawned for free'):format(vehicleName))
    return
  end

  if price > 0 and playerData[src].money < price then
    TriggerClientEvent('koth:purchaseResult', src, false, 'Not enough money')
    print(('[KOTH] Player %d cannot afford to rent %s ($%d) - has $%d'):format(src, vehicleName, price, playerData[src].money))
    return
  end

  -- Deduct money and save to database
  if price > 0 then
    playerData[src].money = playerData[src].money - price
    SavePlayerData(src)
  end

  -- Track rented vehicle (temporary - will be removed on disconnect or after time)
  if not playerRentedVehicles[src] then
    playerRentedVehicles[src] = {}
  end
  playerRentedVehicles[src][vehicleName] = true

  print(('[KOTH] Player %d rented %s for $%d - remaining: $%d'):format(src, vehicleName, price, playerData[src].money))

  -- Update client HUD with new money
  TriggerClientEvent('koth:updatePlayerData', src, playerData[src])

  -- Spawn the vehicle
  SpawnVehicleForPlayer(src, vehicleName, 'rent', price)
  TriggerClientEvent('koth:purchaseResult', src, true, ('Rented %s for $%d - Temporary use only'):format(vehicleName, price))
  
  -- Remove rental after 10 minutes
  Citizen.SetTimeout(600000, function() -- 10 minutes
    if playerRentedVehicles[src] and playerRentedVehicles[src][vehicleName] then
      playerRentedVehicles[src][vehicleName] = nil
      print(('[KOTH] Rental expired for player %d - %s'):format(src, vehicleName))
    end
  end)
end)

-- Command to spawn owned vehicles
RegisterCommand('myvehicles', function(source, args, rawCommand)
  local src = source
  if src == 0 then return end
  
  if not playerOwnedVehicles[src] or next(playerOwnedVehicles[src]) == nil then
    TriggerClientEvent('chat:addMessage', src, {
      color = {255, 0, 0},
      multiline = true,
      args = {"[KOTH]", "You don't own any vehicles. Purchase vehicles to own them permanently!"}
    })
    return
  end
  
  local ownedList = {}
  for vehicleName, _ in pairs(playerOwnedVehicles[src]) do
    table.insert(ownedList, vehicleName)
  end
  
  TriggerClientEvent('chat:addMessage', src, {
    color = {0, 255, 0},
    multiline = true,
    args = {"[KOTH]", "Your owned vehicles: " .. table.concat(ownedList, ", ")}
  })
end, false)

-- Command to spawn a specific owned vehicle
RegisterCommand('spawnvehicle', function(source, args, rawCommand)
  local src = source
  if src == 0 then return end
  
  if not args[1] then
    TriggerClientEvent('chat:addMessage', src, {
      color = {255, 0, 0},
      multiline = true,
      args = {"[KOTH]", "Usage: /spawnvehicle [vehicle name]"}
    })
    return
  end
  
  local vehicleName = args[1]
  
  -- Check if player owns this vehicle
  if playerOwnedVehicles[src] and playerOwnedVehicles[src][vehicleName] then
    SpawnVehicleForPlayer(src, vehicleName, 'owned', 0)
    TriggerClientEvent('koth:purchaseResult', src, true, ('Spawned your owned %s'):format(vehicleName))
  else
    TriggerClientEvent('chat:addMessage', src, {
      color = {255, 0, 0},
      multiline = true,
      args = {"[KOTH]", "You don't own this vehicle! Use /myvehicles to see your owned vehicles."}
    })
  end
end, false)

--[[
  Admin command to grant class XP to a player.  Usage:
    /giveclassxp [serverId] [amount]

  This command is restricted to players with the `koth.admin` ACE
  permission.  It will forward the XP award to the koth_classes
  resource which manages per‑class XP.  The XP will then be saved
  to the database and reflected in the player's class level.
]]
RegisterCommand('giveclassxp', function(source, args, raw)
  local src = source
  -- Only allow console or admins (with koth.admin ACE)
  if src ~= 0 and not IsPlayerAceAllowed(src, 'koth.admin') then
    TriggerClientEvent('chat:addMessage', src, {
      color = {255, 0, 0},
      multiline = true,
      args = {"[KOTH]", "You do not have permission to use this command."}
    })
    return
  end
  local targetId = tonumber(args[1] or '')
  local xpAmount = tonumber(args[2] or '')
  if not targetId or not xpAmount then
    if src ~= 0 then
      TriggerClientEvent('chat:addMessage', src, {
        color = {255, 0, 0},
        multiline = true,
        args = {"[KOTH]", "Usage: /giveclassxp [playerId] [xpAmount]"}
      })
    else
      print('[KOTH] Usage: /giveclassxp [playerId] [xpAmount]')
    end
    return
  end
  -- Forward to koth_classes resource
  TriggerEvent('koth_classes:addClassXP', targetId, xpAmount)
  -- Notify admin
  if src ~= 0 then
    TriggerClientEvent('chat:addMessage', src, {
      color = {0, 255, 0},
      multiline = true,
      args = {"[KOTH]", ("Granted %d class XP to player %d"):format(xpAmount, targetId)}
    })
  else
    print(('[KOTH] Granted %d class XP to player %d'):format(xpAmount, targetId))
  end
end, false)

-- Server-side vehicle spawning function for networking
function SpawnVehicleForPlayer(playerId, vehicleName, purchaseType, price)
  print(('[KOTH] Requesting client to spawn vehicle: %s for player %d'):format(vehicleName, playerId))
  
  -- Notify the client to spawn the vehicle
  TriggerClientEvent('koth:spawnVehicle', playerId, vehicleName, purchaseType, price)
end

-- Add new server events for vehicle shop money requests
RegisterNetEvent('koth:getMoneyForVehicleShop', function(vehicles)
  local src = source
  local playerName = GetPlayerName(src)
  
  print(('[KOTH] Vehicle shop money request from %s'):format(playerName))
  
  -- Get fresh player data
  if playerData[src] and playerData[src].money then
    local playerMoney = playerData[src].money
    print(('[KOTH] Using cached data - Player %s has $%d'):format(playerName, playerMoney))
    
    -- Send owned vehicles list as well
    local ownedList = {}
    if playerOwnedVehicles[src] then
      for vehicleName, _ in pairs(playerOwnedVehicles[src]) do
        table.insert(ownedList, vehicleName)
      end
    end
    
    -- Decorate vehicles with ownership and level locks.  Compute the
    -- player's global level and owned status, then set `locked` and
    -- `owned` fields for each vehicle.  The UI expects a field
    -- `items` containing these decorated vehicle objects.
    local level = playerData[src] and (playerData[src].level or 1) or 1
    local decorated = {}
    for _, veh in ipairs(vehicles or {}) do
      local copy = {}
      for k, v in pairs(veh) do copy[k] = v end
      -- Mark owned
      if playerOwnedVehicles[src] and playerOwnedVehicles[src][veh.name] then
        copy.owned = true
      end
      -- Determine locked state based on requiredLevel and VIP
      local lvlReq = tonumber(veh.requiredLevel or 0)
      local lockedForLevel = (lvlReq > level) and (not copy.owned)
      -- VIP vehicles remain locked until the player has the VIP role.  Use
      -- the IsPlayerVIP helper to decide whether to unlock them.  If
      -- the vehicle is VIP‑only and the player is not VIP, lockedForVip
      -- will be true; otherwise it will be false.
      local lockedForVip = (veh.vipOnly and not IsPlayerVIP(src))
      copy.locked = lockedForLevel or lockedForVip
      table.insert(decorated, copy)
    end
    -- Determine VIP status once at the start of the request.
    local vipStatus = IsPlayerVIP(src)
    TriggerClientEvent('koth:showVehicleShopWithMoney', src, {
      items = decorated,
      money = playerMoney,
      playerLevel = level,
      -- Pass through VIP status so the client can unlock VIP‑only vehicles
      isVip = vipStatus
    })
    -- Send list of owned vehicles separately if needed by other UIs
    TriggerClientEvent('koth:updateOwnedVehicles', src, ownedList)
  else
    print(('[KOTH] No cached data for %s, loading from database...'):format(playerName))
    LoadPlayerData(src)
    
    -- Wait for data to load then send
    Citizen.SetTimeout(1000, function()
      local money = playerData[src] and playerData[src].money or 0
      local ownedList = {}
      if playerOwnedVehicles[src] then
        for vehicleName, _ in pairs(playerOwnedVehicles[src]) do
          table.insert(ownedList, vehicleName)
        end
      end
      -- Decorate vehicles as above but with fallback data
      local level = playerData[src] and (playerData[src].level or 1) or 1
      local decorated = {}
      for _, veh in ipairs(vehicles or {}) do
        local copy = {}
        for k, v in pairs(veh) do copy[k] = v end
        -- Mark owned
        if playerOwnedVehicles[src] and playerOwnedVehicles[src][veh.name] then
          copy.owned = true
        end
        local lvlReq = tonumber(veh.requiredLevel or 0)
        local lockedForLevel = (lvlReq > level) and (not copy.owned)
        -- VIP vehicles remain locked until the player has the VIP role.
        local lockedForVip = (veh.vipOnly and not IsPlayerVIP(src))
        copy.locked = lockedForLevel or lockedForVip
        table.insert(decorated, copy)
      end
      -- Determine VIP status once at the start of the request.
      local vipStatus = IsPlayerVIP(src)
      TriggerClientEvent('koth:showVehicleShopWithMoney', src, {
        items = decorated,
        money = money,
        playerLevel = level,
        isVip = vipStatus
      })
      TriggerClientEvent('koth:updateOwnedVehicles', src, ownedList)
    end)
  end
end)

RegisterNetEvent('koth:getDataForClassShop', function(classes)
  local src = source
  local playerName = GetPlayerName(src)
  
  print(('[KOTH] Class shop data request from %s'):format(playerName))
  
  -- Get fresh player data
  if playerData[src] and playerData[src].money then
    local playerMoney = playerData[src].money
    local playerLevel = playerData[src].level or 1
    print(('[KOTH] Using cached data - Player %s has $%d, Level %d'):format(playerName, playerMoney, playerLevel))
    
    TriggerClientEvent('koth:showClassShopWithData', src, {
      classes = classes,
      money = playerMoney,
      level = playerLevel
    })
  else
    print(('[KOTH] No cached data for %s, loading from database...'):format(playerName))
    LoadPlayerData(src)
    
    -- Wait for data to load then send
    Citizen.SetTimeout(1000, function()
      local money = playerData[src] and playerData[src].money or 0
      local level = playerData[src] and playerData[src].level or 1
      TriggerClientEvent('koth:showClassShopWithData', src, {
        classes = classes,
        money = money,
        level = level
      })
    end)
  end
end)

-- Class selection handler (stub)
RegisterNetEvent('koth:selectClass', function(classId)
  local src = source
  print(('[KOTH] Player %d selected class: %s'):format(src, classId or 'none'))
  -- TODO: Add loadout/class logic
end)

-- Weapon/loadout selection handler (supports both buy and rent)
RegisterNetEvent('koth:selectLoadout', function(classId, weapon, price, purchaseType)
  local src = source
  local weaponPrice = tonumber(price or 0)
  local isRental = purchaseType == 'rent'

  print(('[KOTH] Player %d selected weapon %s for class %s (%s for $%d)'):format(src, weapon or 'none', classId or 'none', purchaseType or 'buy', weaponPrice))
  print(('[KOTH] DEBUG: playerData type: %s, playerData[%d] exists: %s'):format(type(playerData), src, tostring(playerData[src] ~= nil)))

  if not weapon or not classId then
    TriggerClientEvent('koth:purchaseResult', src, false, 'Invalid weapon selection')
    return
  end

  -- Check class level before allowing the purchase.  Players start at
  -- class level 0 and can only purchase free weapons (price = 0) until
  -- they gain enough XP to level up.  This call to the classes
  -- resource queries the player's current class level from memory.  If
  -- the level is 0 and the weapon costs money, deny the purchase.
  local classLevel = 0
  if exports['koth_classes'] and exports['koth_classes'].GetPlayerClassLevel then
    classLevel = exports['koth_classes']:GetPlayerClassLevel(src, classId) or 0
  end
  if classLevel <= 0 and weaponPrice > 0 then
    TriggerClientEvent('koth:purchaseResult', src, false, 'Your class level is too low to purchase this weapon.  Use free weapons to earn XP.')
    return
  end

  -- Ensure playerData table exists
  if not playerData then
    print('[KOTH] CRITICAL ERROR: playerData table is nil!')
    playerData = {}
  end

  -- Check if player has enough money (free weapons are allowed)
  if weaponPrice > 0 then
    print(('[KOTH] DEBUG: Checking money for player %d, playerData[%d] = %s'):format(src, src, tostring(playerData[src])))

    if not playerData[src] then
      print(('[KOTH] Player data not loaded for %d, attempting to load...'):format(src))
      -- Try to load player data immediately
      LoadPlayerData(src)

      -- Give it a moment to load and then check again
      Citizen.SetTimeout(1000, function()
        if not playerData[src] then
          print(('[KOTH] Failed to load player data for %d after retry'):format(src))
          TriggerClientEvent('koth:purchaseResult', src, false, 'Player data not loaded. Please try again.')
        else
          -- Retry the weapon purchase now that data is loaded
          print(('[KOTH] Player data loaded for %d, retrying weapon purchase'):format(src))
          TriggerEvent('koth:selectLoadout', classId, weapon, price, purchaseType)
        end
      end)
      return
    end

    if not playerData[src].money then
      print(('[KOTH] Player %d has invalid money data, playerData[%d] = %s'):format(src, src, json.encode(playerData[src])))
      TriggerClientEvent('koth:purchaseResult', src, false, 'Invalid player data. Please try again.')
      return
    end

    local currentMoney = tonumber(playerData[src].money) or 0
    print(('[KOTH] Player %d money check: has $%d, needs $%d'):format(src, currentMoney, weaponPrice))

    if currentMoney < weaponPrice then
      TriggerClientEvent('koth:purchaseResult', src, false, 'Not enough money')
      print(('[KOTH] Player %d cannot afford weapon %s ($%d) - has $%d'):format(src, weapon, weaponPrice, currentMoney))
      return
    end

    -- Deduct money and save to database
    playerData[src].money = currentMoney - weaponPrice
    SavePlayerData(src)

    print(('[KOTH] Player %d %s weapon %s for $%d - remaining: $%d'):format(src, isRental and 'rented' or 'bought', weapon, weaponPrice, playerData[src].money))

    -- Update client HUD with new money
    TriggerClientEvent('koth:updatePlayerData', src, playerData[src])
  end

  -- Give the player the selected weapon
  TriggerClientEvent('koth:giveWeapon', src, weapon, classId, weaponPrice, isRental)

  if weaponPrice > 0 then
    local actionText = isRental and 'Rented' or 'Purchased'
    TriggerClientEvent('koth:purchaseResult', src, true, ('%s %s for $%d'):format(actionText, weapon:gsub('WEAPON_', ''):gsub('_', ' '), weaponPrice))
  end
  
  -- Close the weapon shop UI after successful purchase/rental
  TriggerClientEvent('koth:closeWeaponShop', src)

  -- If this was a purchase (not a rental) and the player does not already
  -- own the weapon, persist ownership.  Ownership allows the player to
  -- spawn the weapon for free in future rounds without paying again.  We
  -- insert the record into the koth_player_owned_weapons table keyed
  -- by the player's txid/license and class ID.  The upsert (INSERT
  -- IGNORE) prevents duplicate entries if the player buys the same
  -- weapon again.
  if not isRental and weaponPrice > 0 then
    -- Ensure in‑memory table exists for this player and class
    if not playerOwnedWeapons[src] then playerOwnedWeapons[src] = {} end
    if not playerOwnedWeapons[src][classId] then playerOwnedWeapons[src][classId] = {} end
    -- Only save if not already owned
    if not playerOwnedWeapons[src][classId][weapon] then
      -- Mark as owned in memory
      playerOwnedWeapons[src][classId][weapon] = true
      -- Persist to DB
      local identifiers = GetPlayerIdentifiers(src)
      local txid
      for _, id in ipairs(identifiers) do
        if string.find(id, 'license:') then txid = id break end
      end
      if txid then
        MySQL.Async.execute(
          'INSERT IGNORE INTO koth_player_owned_weapons (txid, class, weapon) VALUES (@txid, @class, @weapon)',
          { ['@txid'] = txid, ['@class'] = classId, ['@weapon'] = weapon },
          function(affected)
            if affected and affected > 0 then
              print(('[KOTH] Persisted owned weapon %s for player %d (class %s)'):format(weapon, src, classId))
            end
          end
        )
      end
    end
  end
end)

-- DATABASE CONFIGURATION
-- Configured for your Zap Hosting MySQL database
local DB_CONFIG = {
  host = "mysql-mariadb-oce02-11-101.zap-srv.com",
  port = 3306,
  database = "zap1190649-1",
  username = "zap1190649-1",
  password = "tYXDWyEmvqykO75w"
}

-- MONEY AND XP SYSTEM
-- playerData moved to top of file

-- Get player identifiers
function GetPlayerIdentifiers(source)
  local identifiers = {
    txid = nil,
    steam = nil,
    discord = nil
  }

  for i = 0, GetNumPlayerIdentifiers(source) - 1 do
    local id = GetPlayerIdentifier(source, i)

    if string.find(id, "steam:") then
      identifiers.steam = id
    elseif string.find(id, "discord:") then
      identifiers.discord = id
    end
  end

  -- Use license as primary identifier (TX Admin compatible)
  identifiers.txid = GetPlayerIdentifierByType(source, 'license') or identifiers.steam or ('temp_' .. tostring(source))

  return identifiers
end

-- Load player data from database (no team restoration)
function LoadPlayerData(source)
  print(('[KOTH] LoadPlayerData called with source: %s (type: %s)'):format(tostring(source), type(source)))

  local identifiers = GetPlayerIdentifiers(source)
  local playerName = GetPlayerName(source)

  if not identifiers.txid then
    print(('[KOTH] ERROR: Could not get identifier for player %d'):format(source))
    return
  end

  -- Load from database
  print(('[KOTH] Attempting to load player data for %s (TXID: %s)'):format(playerName, identifiers.txid))

  exports.oxmysql:execute('SELECT * FROM koth_players WHERE txid = ?', {identifiers.txid}, function(result)
    print(('[KOTH] Database query completed for %s, result type: %s'):format(playerName, type(result)))

    if result then
      print(('[KOTH] Database result length: %d'):format(#result))
      if result[1] then
        print(('[KOTH] Found existing player data: Money=%s, XP=%s, Level=%s'):format(
          tostring(result[1].money), tostring(result[1].xp), tostring(result[1].level)))
      end
    end

    if result and result[1] then
      -- Player exists, load data
      playerData[source] = result[1]
      -- Ensure prestige fields exist; fallback to 0 if nil.  When
      -- persisting these values you should have corresponding columns
      -- (prestige_rank, prestige_weapon_tokens, prestige_vehicle_tokens)
      -- in your koth_players table.
      playerData[source].prestigeRank = result[1].prestige_rank or 0
      playerData[source].prestigeWeaponTokens = result[1].prestige_weapon_tokens or 0
      playerData[source].prestigeVehicleTokens = result[1].prestige_vehicle_tokens or 0
      print(('[KOTH] Loaded existing player data for %s - Money: $%d, XP: %d, Level: %d'):format(
        playerName, result[1].money, result[1].xp, result[1].level))

      -- Determine VIP status for this player and store it.  This
      -- property will be included in the playerData table sent to
      -- clients so that the UI can reflect VIP privileges.
      playerData[source].isVip = IsPlayerVIP(source)

      -- Load owned vehicles for player
      exports.oxmysql:execute('SELECT vehicle_name FROM koth_player_vehicles WHERE txid = ?', {identifiers.txid}, function(vehicleResults)
        playerOwnedVehicles[source] = {}
        if vehicleResults then
          for _, row in ipairs(vehicleResults) do
            playerOwnedVehicles[source][row.vehicle_name] = true
          end
          print(('[KOTH] Loaded %d owned vehicles for player %s'):format(#vehicleResults, playerName))
        else
          print(('[KOTH] No owned vehicles found for player %s'):format(playerName))
        end
        -- If the player has the VIP role, automatically add the VIP car
        -- to their owned vehicles list.  This grants them access to
        -- spawn the VIP car without purchasing it.  The car is not
        -- persisted to the database so that revoking the role will
        -- remove access on next load.
        if IsPlayerVIP(source) then
          playerOwnedVehicles[source]['VIP Car'] = true
        end
      end)

      -- Refresh the player's class level before sending.  This
      -- ensures the client receives an up‑to‑date classLevel field
      -- alongside money, XP and kills.
      refreshPlayerClassLevel(source)
      -- Send data immediately
      TriggerClientEvent('koth:updatePlayerData', source, playerData[source])
      print(('[KOTH] Sent existing player data to client for %s'):format(playerName))

    else
      -- New player, create record
      print(('[KOTH] Creating new player record for %s'):format(playerName))
      exports.oxmysql:execute('INSERT INTO koth_players (txid, steam_id, discord_id, player_name, money, xp, level, prestige_rank, prestige_weapon_tokens, prestige_vehicle_tokens) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', {
        identifiers.txid,
        identifiers.steam,
        identifiers.discord,
        playerName,
        1000,
        0,
        1,
        0,
        0,
        0
      }, function(insertId)
        -- Create local data with prestige fields initialised
        playerData[source] = {
          id = insertId,
          txid = identifiers.txid,
          steam_id = identifiers.steam,
          discord_id = identifiers.discord,
          player_name = playerName,
          money = 1000,
          xp = 0,
          level = 1,
          kills = 0,
          deaths = 0,
          zone_kills = 0,
          prestigeRank = 0,
          prestigeWeaponTokens = 0,
          prestigeVehicleTokens = 0
        }

        -- Record VIP status for the new player.  This will be sent
        -- along with other player data so the client can display
        -- appropriate UI (e.g., VIP bonus indicators).
        playerData[source].isVip = IsPlayerVIP(source)

        -- Initialise owned vehicles for new players
        playerOwnedVehicles[source] = playerOwnedVehicles[source] or {}
        -- If the new player has the VIP role, automatically grant the
        -- VIP car.  This does not persist the car to the database so
        -- removing the VIP role will remove access upon rejoin.
        if playerData[source].isVip then
          playerOwnedVehicles[source]['VIP Car'] = true
        end

        print(('[KOTH] Created new player record for %s - Money: $%d, XP: %d, Level: %d'):format(
          playerName, 1000, 0, 1))

        -- Send data immediately
        TriggerClientEvent('koth:updatePlayerData', source, playerData[source])
        print(('[KOTH] Sent new player data to client for %s'):format(playerName))
      end)
    end
  end)

  -- Fallback: Create local data if database fails
  Citizen.SetTimeout(10000, function() -- Wait 10 seconds
    if not playerData[source] then
      print(('[KOTH] Database failed, creating fallback data for %s'):format(playerName))
      playerData[source] = {
        txid = identifiers.txid,
        steam_id = identifiers.steam,
        discord_id = identifiers.discord,
        player_name = playerName,
        money = 1000,
        xp = 0,
        level = 1,
        kills = 0,
        deaths = 0,
        zone_kills = 0
      }
      TriggerClientEvent('koth:updatePlayerData', source, playerData[source])
    end
  end)
end

-- Save player data to database (no team saving)
function SavePlayerData(source)
  if not playerData[source] then return end

  local data = playerData[source]

  exports.oxmysql:execute('UPDATE koth_players SET money = ?, xp = ?, level = ?, kills = ?, deaths = ?, zone_kills = ?, player_name = ?, prestige_rank = ?, prestige_weapon_tokens = ?, prestige_vehicle_tokens = ? WHERE txid = ?', {
    data.money,
    data.xp,
    data.level,
    data.kills,
    data.deaths,
    data.zone_kills,
    data.player_name,
    data.prestigeRank or 0,
    data.prestigeWeaponTokens or 0,
    data.prestigeVehicleTokens or 0,
    data.txid
  }, function(result)
    if result and (type(result) == 'number' and result > 0) or (type(result) == 'table' and result.affectedRows and result.affectedRows > 0) then
      print(('[KOTH] Saved data for player %s'):format(data.player_name))
    end
  end)
end

-- Player data request event
RegisterNetEvent('koth:requestPlayerData', function()
  local source = source
  print(('[KOTH] Player data requested by %s'):format(GetPlayerName(source)))

  if playerData[source] then
    TriggerClientEvent('koth:updatePlayerData', source, playerData[source])
    print(('[KOTH] Sent requested player data to %s'):format(GetPlayerName(source)))
  else
    print(('[KOTH] No player data available for %s, loading from database...'):format(GetPlayerName(source)))
    LoadPlayerData(source)
  end
end)

-- Prestige: handle player prestige requests.  A player can prestige if
-- they have reached level 50 and have not yet prestiged 5 times.  On
-- prestige, the player's level and XP are reset to 1 and 0,
-- respectively.  They receive two tokens (one weapon, one vehicle)
-- which can later be spent to permanently unlock items from the
-- prestige shop.  The player's prestige rank is incremented.  All
-- changes are saved to the database via SavePlayerData().
RegisterNetEvent('koth:prestige')
AddEventHandler('koth:prestige', function()
  local src = source
  local pdata = playerData[src]
  if not pdata then return end
  -- Ensure level and prestige fields exist
  local level = tonumber(pdata.level or 0)
  local rank = tonumber(pdata.prestigeRank or 0)
  if level < 50 then
    TriggerClientEvent('chat:addMessage', src, { color = {255, 0, 0}, args = { '[KOTH]', 'You must reach level 50 to prestige.' } })
    return
  end
  if rank >= 5 then
    TriggerClientEvent('chat:addMessage', src, { color = {255, 0, 0}, args = { '[KOTH]', 'You have reached the maximum prestige level.' } })
    return
  end
  -- Reset XP and level
  pdata.xp = 0
  pdata.level = 1
  pdata.prestigeRank = rank + 1
  -- Award two tokens
  pdata.prestigeWeaponTokens = (pdata.prestigeWeaponTokens or 0) + 1
  pdata.prestigeVehicleTokens = (pdata.prestigeVehicleTokens or 0) + 1
  -- Persist changes
  SavePlayerData(src)
  -- Inform the player
  TriggerClientEvent('chat:addMessage', src, { color = {0, 255, 0}, args = { '[KOTH]', string.format('You have prestiged! Prestige %d. You received one weapon token and one vehicle token.', pdata.prestigeRank) } })
  -- Update HUD
  TriggerClientEvent('koth:updatePlayerData', src, pdata)
end)

-- Provide prestige data to the client on request.  The client may
-- request this data to display token counts in the Prestige shop UI.
RegisterNetEvent('koth:getPrestigeData')
AddEventHandler('koth:getPrestigeData', function()
  local src = source
  local pdata = playerData[src]
  if pdata then
    TriggerClientEvent('koth:receivePrestigeData', src, {
      prestigeRank = pdata.prestigeRank or 0,
      weaponTokens = pdata.prestigeWeaponTokens or 0,
      vehicleTokens = pdata.prestigeVehicleTokens or 0
    })
  end
end)

--[[
  Handle prestige weapon purchases.  Players can spend one prestige
  weapon token to permanently unlock a VIP weapon across all classes.
  The weapon is added to their owned weapons list for each class so
  that it can be spawned for free in future rounds.  Tokens are
  deducted and the purchase is persisted to the database.  If the
  player lacks tokens or already owns the weapon, an informative
  message is sent and no changes are made.

  Parameters:
    weaponId (string): The internal weapon identifier (e.g. 'WEAPON_SCAR').
]]
RegisterNetEvent('koth:purchasePrestigeWeapon')
AddEventHandler('koth:purchasePrestigeWeapon', function(weaponId)
  local src = source
  local weapon = tostring(weaponId or '')
  local pdata = playerData[src]
  if not pdata or weapon == '' then
    return
  end
  local tokens = tonumber(pdata.prestigeWeaponTokens or 0)
  if tokens <= 0 then
    TriggerClientEvent('chat:addMessage', src, {
      color = {255, 0, 0},
      args = { '[KOTH]', 'You do not have any prestige weapon tokens.' }
    })
    return
  end
  -- Check if the player already owns this weapon for any class
  local alreadyOwned = false
  if playerOwnedWeapons[src] then
    for classId, weapons in pairs(playerOwnedWeapons[src]) do
      if weapons and weapons[weapon] then
        alreadyOwned = true
        break
      end
    end
  end
  if alreadyOwned then
    TriggerClientEvent('chat:addMessage', src, {
      color = {255, 0, 0},
      args = { '[KOTH]', 'You already own this prestige weapon.' }
    })
    return
  end
  -- Deduct one token
  pdata.prestigeWeaponTokens = tokens - 1
  -- Unlock the weapon for each class.  We list the known class IDs
  -- here; if you add more classes to the koth_classes resource
  -- update this array accordingly.  Adding across all classes
  -- ensures the player can use the prestige weapon regardless of
  -- class selection.
  local classes = { 'assault', 'medic', 'engineer', 'heavy', 'scout' }
  if not playerOwnedWeapons[src] then playerOwnedWeapons[src] = {} end
  -- Persist ownership for each class and update in‑memory table
  local ids = GetPlayerIdentifiers(src)
  local txid = ids and ids.txid
  for _, classId in ipairs(classes) do
    if not playerOwnedWeapons[src][classId] then
      playerOwnedWeapons[src][classId] = {}
    end
    if not playerOwnedWeapons[src][classId][weapon] then
      playerOwnedWeapons[src][classId][weapon] = true
      if txid then
        -- Use the same MySQL API as the rest of the resource.  We rely on
        -- MySQL.Async.execute to insert the owned weapon record.  The
        -- INSERT IGNORE avoids duplicate entries when a player re‑unlocks
        -- the same weapon.
        MySQL.Async.execute(
          'INSERT IGNORE INTO koth_player_owned_weapons (txid, class, weapon) VALUES (@txid, @class, @weapon)',
          { ['@txid'] = txid, ['@class'] = classId, ['@weapon'] = weapon },
          function() end
        )
      end
    end
  end
  -- Persist token and other data
  SavePlayerData(src)
  -- Notify the player of success
  -- Immediately equip the prestige weapon so the player can use it
  -- right away.  Pass a custom classId of 'prestige' and price 0 so
  -- the client treats it as a free weapon.
  TriggerClientEvent('koth:giveWeapon', src, weapon, 'prestige', 0)
  -- Inform the player that the weapon has been unlocked and will
  -- persist across sessions.  The weapon becomes permanently
  -- available in the weapon shop for all classes.
  TriggerClientEvent('chat:addMessage', src, {
    color = {0, 255, 0},
    args = { '[KOTH]', string.format('Unlocked prestige weapon: %s (equipped and available permanently)', weapon:gsub('WEAPON_', ''):gsub('_', ' ')) }
  })
  -- Update HUD to reflect new token count
  TriggerClientEvent('koth:updatePlayerData', src, pdata)
end)

--[[
  Handle prestige vehicle purchases.  Players spend one prestige
  vehicle token to permanently unlock the Insurgent2.  The vehicle
  becomes part of their owned vehicles list and is spawned for free
  upon purchase.  Tokens are deducted and the ownership is
  persisted.  If the player lacks tokens or already owns the
  vehicle, the purchase is denied.

  Parameters:
    vehicleName (string): Optional name of the vehicle to unlock;
                          defaults to 'Insurgent2'.
]]
RegisterNetEvent('koth:purchasePrestigeVehicle')
AddEventHandler('koth:purchasePrestigeVehicle', function(vehicleName)
  local src = source
  local pdata = playerData[src]
  if not pdata then return end
  local tokens = tonumber(pdata.prestigeVehicleTokens or 0)
  if tokens <= 0 then
    TriggerClientEvent('chat:addMessage', src, {
      color = {255, 0, 0},
      args = { '[KOTH]', 'You do not have any prestige vehicle tokens.' }
    })
    return
  end
  local veh = vehicleName or 'Insurgent2'
  if not playerOwnedVehicles[src] then playerOwnedVehicles[src] = {} end
  if playerOwnedVehicles[src][veh] then
    TriggerClientEvent('chat:addMessage', src, {
      color = {255, 0, 0},
      args = { '[KOTH]', 'You already own this prestige vehicle.' }
    })
    return
  end
  -- Deduct token and record ownership
  pdata.prestigeVehicleTokens = tokens - 1
  playerOwnedVehicles[src][veh] = true
  -- Persist to database
  local ids = GetPlayerIdentifiers(src)
  local txid = ids and ids.txid
  if txid then
    -- Insert the owned vehicle using the same MySQL wrapper used for
    -- standard purchases.  INSERT IGNORE prevents duplicate entries.
    MySQL.Async.execute(
      'INSERT IGNORE INTO koth_player_vehicles (txid, vehicle_name) VALUES (@txid, @vehicle)',
      { ['@txid'] = txid, ['@vehicle'] = veh },
      function() end
    )
  end
  -- Save player data (tokens)
  SavePlayerData(src)
  -- Spawn the vehicle for the player immediately
  SpawnVehicleForPlayer(src, veh, 'prestige', 0)
  -- Inform the player and clarify that the vehicle will spawn
  -- immediately and remain available permanently.  The player can
  -- respawn the vehicle from the vehicle shop at any time without
  -- using tokens.
  TriggerClientEvent('chat:addMessage', src, {
    color = {0, 255, 0},
    args = { '[KOTH]', string.format('Unlocked prestige vehicle: %s (spawning now and available permanently)', veh) }
  })
  -- Update HUD to reflect new token count
  TriggerClientEvent('koth:updatePlayerData', src, pdata)
end)

-- Direct money request for weapon shop (guaranteed fix)
RegisterNetEvent('koth:getMoneyForWeaponShop', function(classId, weapons)
  local source = source
  local playerName = GetPlayerName(source)

  print(('[KOTH] Direct money request for weapon shop by %s'):format(playerName))

  -- Ensure player data is loaded
  if not playerData[source] then
    print(('[KOTH] Player data not loaded for %d, loading now...'):format(source))
    LoadPlayerData(source)
    
    -- Wait a moment for data to load
    Citizen.SetTimeout(500, function()
      if playerData[source] then
        -- When sending fallback data before ownership and class level have been computed,
        -- include classLevel as 0 so the UI can still render a level indicator.
        -- Determine VIP status for this player when sending fallback data
        local vipStatus = IsPlayerVIP(source)
        TriggerClientEvent('koth:showWeaponShopWithMoney', source, {
          class = classId,
          weapons = weapons,
          money = playerData[source].money or 0,
          classLevel = 0,
          -- Pass VIP flag so the client can display VIP weapons appropriately
          isVip = vipStatus
        })
      else
        -- Fallback if data still not loaded
        local vipStatus = IsPlayerVIP(source)
        TriggerClientEvent('koth:showWeaponShopWithMoney', source, {
          class = classId,
          weapons = weapons,
          money = 0,
          classLevel = 0,
          isVip = vipStatus
        })
      end
    end)
    return
  end

  -- Determine the player's class level for the selected class.  If the
  -- classes resource is available, query it; otherwise default to 0.
  local classLevel = 0
  if exports['koth_classes'] and exports['koth_classes'].GetPlayerClassLevel then
    classLevel = exports['koth_classes']:GetPlayerClassLevel(source, classId) or 0
  end
  -- Load owned weapons for this class so we can mark them in the UI
  loadOwnedWeapons(source, classId)
  local ownedForClass = (playerOwnedWeapons[source] and playerOwnedWeapons[source][classId]) or {}
  --[[
    Decorate weapon data with ownership, level requirements and a sorted
    order based on price.  The server owner requested that weapons be
    unlocked sequentially as the player levels up their class.  To
    achieve this we sort all paid (non‑free) weapons by their price in
    ascending order and assign an increasing requiredLevel starting at
    1.  Free weapons (price of 0 or explicit `free` flag) always
    remain at requiredLevel 0.  VIP‑only weapons are exempt from this
    ordering and will appear at the end of the list.

    Once the required levels are determined we mark each weapon as
    locked if the player's current class level is below the
    requirement and the weapon is not already owned.  Owned weapons
    bypass class level restrictions, allowing players to spawn them
    for free in future rounds.
  ]]
  local decorated = {}

  -- Build a list of paid weapons (non‑free, non‑VIP) for sorting
  local paidWeapons = {}
  for _, item in ipairs(weapons or {}) do
    local price = tonumber(item.price or 0)
    local freeFlag = item.free
    if not item.vipOnly and not freeFlag and price > 0 then
      table.insert(paidWeapons, item)
    end
  end
  -- Sort paid weapons by ascending price
  table.sort(paidWeapons, function(a, b)
    return (tonumber(a.price or 0) < tonumber(b.price or 0))
  end)
  -- Create a mapping of weapon name to required level based on sort order
  local requiredLevels = {}
  for idx, itm in ipairs(paidWeapons) do
    requiredLevels[itm.weapon] = idx
  end

  -- Custom required level overrides for certain classes.  The server owner
  -- requested that for the Heavy class, the Combat MG should unlock
  -- significantly later than other weapons.  To achieve this we set
  -- its required level to 10 regardless of its price sort order.  The
  -- MG itself remains a free weapon and will always have a required
  -- level of 0 (see weapon definitions).
  if classId == 'heavy' then
    requiredLevels['WEAPON_COMBATMG'] = 10
  end

  -- Decorate each incoming weapon item
  for _, item in ipairs(weapons or {}) do
    local copy = {}
    for k, v in pairs(item) do copy[k] = v end
    -- Mark owned if present in DB
    if ownedForClass and ownedForClass[item.weapon] then
      copy.owned = true
    end
    local price = tonumber(item.price or 0)
    local freeFlag = item.free
    -- Determine required level: free items require 0, paid items require
    -- their assigned level from the sorted list, VIP items use a very
    -- high level so they always appear last but will be handled by the
    -- VIP lock logic on the client.
    if freeFlag or price == 0 then
      copy.requiredLevel = 0
    elseif item.vipOnly then
      copy.requiredLevel = math.huge
    elseif requiredLevels[item.weapon] then
      copy.requiredLevel = requiredLevels[item.weapon]
    else
      -- Fallback to level 1 if not in mapping
      copy.requiredLevel = 1
    end
    -- Determine lock state: locked if player's class level is below required
    -- level and the weapon is not already owned and is not VIP only (VIP
    -- items are gated separately by the UI).
    copy.locked = (not copy.owned) and (copy.requiredLevel ~= math.huge) and (classLevel < copy.requiredLevel)
    table.insert(decorated, copy)
  end
  -- Sort decorated list by requiredLevel (free first, then ascending
  -- levels) while preserving VIP items at the end.  If two items
  -- share the same level, fall back to price comparison.
  table.sort(decorated, function(a, b)
    local lvlA = a.requiredLevel or 0
    local lvlB = b.requiredLevel or 0
    if lvlA ~= lvlB then
      return lvlA < lvlB
    end
    -- If both require the same level (including math.huge for VIP),
    -- sort by price ascending to provide a consistent ordering
    return (tonumber(a.price or 0) < tonumber(b.price or 0))
  end)
  -- Send decorated list to client along with class level
  local vipStatus = IsPlayerVIP(source)
  TriggerClientEvent('koth:showWeaponShopWithMoney', source, {
    class = classId,
    weapons = decorated,
    money = playerData[source].money or 0,
    classLevel = classLevel,
    -- Include class XP and max XP so the weapon shop can display
    -- a progress bar for the current class.  Defaults to 0 when
    -- unavailable.
    classXP = (playerData[source] and playerData[source].classXP) or 0,
    classMaxXP = (playerData[source] and playerData[source].classMaxXP) or 0,
    -- Pass VIP status to the weapon shop so VIP‑only weapons can be gated
    isVip = vipStatus
  })
end)

-- Player connecting event
AddEventHandler('playerConnecting', function()
  local source = source
  print(('[KOTH] playerConnecting event - source: %s (type: %s)'):format(tostring(source), type(source)))
end)

-- Player joined event (better for loading data)
AddEventHandler('playerJoining', function()
  local source = source
  print(('[KOTH] playerJoining event - source: %s'):format(tostring(source)))
  -- Load data immediately
  LoadPlayerData(source)
  -- Load owned weapons for all classes so the shop can reflect ownership
  loadOwnedWeapons(source)
end)

-- Also load on player spawn for reliability
RegisterNetEvent('playerSpawned', function()
  local source = source
  if not playerData[source] then
    print(('[KOTH] Player spawned without data, loading for: %s'):format(tostring(source)))
    LoadPlayerData(source)
  end
end)

-- Load data when client is ready (new event)
RegisterNetEvent('koth:clientReady', function()
  local source = source
  print(('[KOTH] Client ready event from player %s'):format(GetPlayerName(source)))
  
  -- Load player data if not already loaded
  if not playerData[source] then
    print(('[KOTH] Loading data for ready client %s'):format(GetPlayerName(source)))
    LoadPlayerData(source)
  else
    -- If data already loaded, send it immediately
    print(('[KOTH] Sending existing data to ready client %s'):format(GetPlayerName(source)))
    TriggerClientEvent('koth:updatePlayerData', source, playerData[source])
  end
  
  -- Also send current team counts and zone points
  TriggerClientEvent('koth:updateTeamCounts', source, teamCounts)
  TriggerClientEvent('koth:updateZonePoints', source, zonePoints)

  -- Send the current team spawn information to the joining client.  Without
  -- explicitly syncing the spawns here, clients that connect during the
  -- first round will fallback to the placeholder spawn values defined in
  -- client.lua and client_death.lua.  This leads to players spawning at
  -- random or incorrect locations on their first team selection.  By
  -- sending the spawns on clientReady we ensure the team selection peds
  -- and initial spawn points reflect the active map (which is always
  -- the quarry on a fresh resource start).  Clients will also update
  -- their death system spawns via the shared koth:updateTeamSpawns event.
  TriggerClientEvent('koth:updateTeamSpawns', source, teamSpawns)

  -- Synchronize the current map configuration for the joining player.
  -- Without this, clients that join mid‑round may still use the default
  -- map (zone and spawn) defined on the client.  We reuse the same
  -- structure sent during map rotations so that the client can update
  -- its KOTH zone coordinates, spawns, and map index.  This does not
  -- rotate the map on the server; it only informs the new client of
  -- the existing configuration.  If additional fields are added to
  -- koth:rotateMap in the future they should be mirrored here.
  TriggerClientEvent('koth:rotateMap', source, {
    zone = mapLocations[currentMapIndex].zone,
    spawns = teamSpawns,
    mapIndex = currentMapIndex
  })

  -- Prestige shop peds are now spawned client‑side as part of the
  -- team ped spawns.  We no longer spawn a separate prestige ped from
  -- the server when a player loads.  See client.lua for details.
end)

-- Backup: Resource start event for existing players
AddEventHandler('onResourceStart', function(resourceName)
  if GetCurrentResourceName() ~= resourceName then return end

  print('[KOTH] Resource started, loading data for existing players...')
  -- Load immediately for all players
  for _, playerId in ipairs(GetPlayers()) do
    print(('[KOTH] Loading data for existing player: %s'):format(playerId))
    LoadPlayerData(tonumber(playerId))
  end

  -- Prestige shop peds now spawn client‑side as part of the team ped
  -- configuration.  We no longer spawn a standalone prestige ped on
  -- resource start.
end)

-- KOTH ZONE SYSTEM
-- Make kothZone global as well so that functions defined earlier in the file
-- (such as RotateMap) can access and modify it.  Lua's 'local' keyword
-- restricts visibility to this scope, so without exposing it to the global
-- table, functions defined above this point would see nil.  By assigning
-- the same table to _G.kothZone we preserve local scope while making a
-- global reference available.  Note: we keep the 'local' declaration so
-- that kothZone isn't accidentally overwritten elsewhere.  See
-- https://www.lua.org/pil/14.4.html for details on global and local scope.
local kothZone = {
  controllingTeam = nil, -- 'red', 'blue', 'green', or nil for neutral
  captureProgress = 0.0, -- 0.0 to captureThreshold
  -- Capture rate (progress gained per second).  Leave this at 1.0 so
  -- captureProgress increments once per second.  Combined with a
  -- captureThreshold of 5.0 below, this yields one point every 5
  -- seconds of uncontested control.
  captureRate = 1.0,
  -- Threshold of capture progress required to award a zone point.  Setting
  -- this to 5.0 means that with captureRate = 1.0 the controlling team
  -- gains a point every 5 seconds.  Previously this was set to 1.0,
  -- resulting in a point every second.
  captureThreshold = 5.0,
  decayRate = 0.5, -- Points lost per second when no players present
  contestedDecayRate = 0.25, -- Points lost per second when contested
  playersInZone = {
    red = 0,
    blue = 0,
    green = 0
  },
  dominantTeam = nil, -- Team with most players
  isContested = false, -- True when multiple teams present
  -- Tracks the last contested count string sent to chat.  When the
  -- composition of teams in the zone changes (e.g. a third team enters),
  -- this value will differ and a new contested chat message will be
  -- broadcast.  Resetting this to nil when the zone becomes neutral or
  -- controlled ensures that contested messages are sent on the next
  -- contested state.
  lastContestedCountStr = nil
}

-- Expose kothZone on the global environment so functions defined earlier in
-- this file (e.g. RotateMap) can access it.  Without this assignment,
-- references to kothZone in RotateMap would be nil because kothZone is
-- declared as a local variable further down in the file.  This assignment
-- happens immediately after the table is created.
_G.kothZone = kothZone

-- Track players in KOTH zone
RegisterNetEvent('koth:playerEnteredZone', function(team)
  local source = source
  if not team then return end

  -- Increment player count for team
  kothZone.playersInZone[team] = (kothZone.playersInZone[team] or 0) + 1
  print(('[KOTH] Player %s entered zone (Team: %s)'):format(source, team))

  -- Track this player in the zonePlayers table for zone bonus payouts.
  -- When a player enters the zone we record their server id keyed by
  -- team.  This allows us to award XP/money only to players actually
  -- standing in the capture area when their team controls the zone.
  if not zonePlayers then
    zonePlayers = { red = {}, blue = {}, green = {} }
  end
  if zonePlayers[team] then
    zonePlayers[team][source] = true
  end

  -- Update zone status
  UpdateKothZoneStatus()
end)

RegisterNetEvent('koth:playerLeftZone', function(team)
  local source = source
  if not team then return end

  -- Decrement player count for team
  if kothZone.playersInZone[team] and kothZone.playersInZone[team] > 0 then
    kothZone.playersInZone[team] = kothZone.playersInZone[team] - 1
  end
  print(('[KOTH] Player %s left zone (Team: %s)'):format(source, team))

  -- Remove this player from zonePlayers tracking.  When a player
  -- leaves the zone we no longer award them zone control bonuses.
  if zonePlayers and zonePlayers[team] then
    zonePlayers[team][source] = nil
  end

  -- Update zone status
  UpdateKothZoneStatus()
end)

-- Admin command to manually grant zone points to a team.  Useful for
-- testing scoring and map rotation without waiting for captures.  Usage:
-- /giveteampoints <team> <amount>  (team = red, blue or green)
RegisterCommand('giveteampoints', function(source, args, raw)
  -- Only allow from server console or users with the "koth.admin" ACE
  local src = source
  if src ~= 0 and not IsPlayerAceAllowed(src, 'koth.admin') then
    -- Not allowed
    TriggerClientEvent('chat:addMessage', src, {
      color = {255, 0, 0},
      multiline = false,
      args = {'[KOTH]', 'You do not have permission to use this command.'}
    })
    return
  end
  local team = args[1] and string.lower(args[1]) or nil
  local amount = tonumber(args[2]) or 0
  if not team or not zonePoints[team] then
    print('[KOTH] /giveteampoints invalid team:', tostring(team))
    if src ~= 0 then
      TriggerClientEvent('chat:addMessage', src, {
        color = {255, 0, 0},
        multiline = false,
        args = {'[KOTH]', 'Invalid team specified. Use red, green or blue.'}
      })
    end
    return
  end
  -- Apply points
  zonePoints[team] = zonePoints[team] + amount
  print(('[KOTH] Admin granted %d points to %s team (Total: %d)'):format(amount, team, zonePoints[team]))
  -- Update clients
  TriggerClientEvent('koth:updateZonePoints', -1, zonePoints)
  -- Check for round end
  if zonePoints[team] >= POINTS_TO_WIN then
    print(('[KOTH] %s team reached %d points via admin command! Ending round...'):format(team, POINTS_TO_WIN))
    TriggerClientEvent('koth:roundEnd', -1, team)
    SetTimeout(10000, function()
      StartMapVote()
    end)
  end
end, true)

-- Clean up when player disconnects
AddEventHandler('playerDropped', function()
  local source = source
  local playerTeam = GetPlayerTeam(source)

  if playerTeam then
    -- Remove from team tracking
    playerTeams[source] = nil
    print(('[KOTH] Player %s disconnected from team %s'):format(source, playerTeam))

    -- Update team counts
    UpdateTeamCounts()

    -- Remove from KOTH zone if present
    if kothZone.playersInZone[playerTeam] and kothZone.playersInZone[playerTeam] > 0 then
      kothZone.playersInZone[playerTeam] = kothZone.playersInZone[playerTeam] - 1
      print(('[KOTH] Player %s removed from KOTH zone count'):format(source))
      UpdateKothZoneStatus()
    end
  end

  -- Clean up vehicle ownership tracking
  playerRentedVehicles[source] = nil -- Remove all rentals on disconnect
  -- Note: playerOwnedVehicles[source] is kept for when they reconnect

  -- Save player data and clean up
  SavePlayerData(source)
  playerData[source] = nil
end)

-- Update KOTH zone status (dominant team, contested status)
function UpdateKothZoneStatus()
  local teamsInZone = {}
  local totalPlayers = 0

  -- Count teams and players in zone
  for team, count in pairs(kothZone.playersInZone) do
    if count > 0 then
      table.insert(teamsInZone, team)
      totalPlayers = totalPlayers + count
    end
  end

  local teamsPresent = #teamsInZone
  local isContested = teamsPresent > 1

  print(('[KOTH] Zone update - Teams present: %d, Total players: %d, Contested: %s'):format(
    teamsPresent, totalPlayers, tostring(isContested)
  ))

  -- Handle zone control based on teams present
  if teamsPresent == 0 then
    -- No teams in zone - becomes neutral
    if kothZone.controllingTeam ~= nil then
      local wasControlled = kothZone.controllingTeam
      kothZone.controllingTeam = nil
      kothZone.captureProgress = 0
      kothZone.dominantTeam = nil
      kothZone.isContested = false
      -- Reset last contested count string when the zone becomes neutral
      kothZone.lastContestedCountStr = nil
      print('[KOTH] Zone is now neutral - no teams present')
      TriggerClientEvent('koth:zoneControlChanged', -1, nil)
      
      -- Notify zone is now neutral
      TriggerClientEvent('chat:addMessage', -1, {
        color = {128, 128, 128},
        multiline = false,
        args = {'[ZONE]', 'Zone is now neutral - no teams present'}
      })
    end
  elseif teamsPresent == 1 then
    -- Single team in zone - instant control
    local singleTeam = teamsInZone[1]
    if kothZone.controllingTeam ~= singleTeam then
      local wasContested = kothZone.isContested
      kothZone.controllingTeam = singleTeam
      kothZone.captureProgress = 0
      kothZone.dominantTeam = singleTeam
      kothZone.isContested = false
      print(('[KOTH] Zone instantly controlled by %s team!'):format(singleTeam))
      TriggerClientEvent('koth:zoneControlChanged', -1, singleTeam)
      
      -- Notify all players in chat with team color
      local teamColors = {
        red = {255, 0, 0},
        green = {0, 255, 0},
        blue = {0, 100, 255}
      }
      local color = teamColors[singleTeam] or {255, 255, 255}
      local message = wasContested and ('%s team has taken the zone!'):format(singleTeam:upper()) or ('%s team has control of the zone!'):format(singleTeam:upper())
      TriggerClientEvent('chat:addMessage', -1, {
        color = color,
        multiline = false,
        args = {'[ZONE]', message}
      })
    end
    kothZone.dominantTeam = singleTeam
    kothZone.isContested = false
    -- Reset last contested count string when a single team controls the zone
    kothZone.lastContestedCountStr = nil
  else
    -- Multiple teams are present in the zone.  Determine which team has the
    -- highest player count (dominant) and whether that team has a clear
    -- majority.  When a majority exists, that team takes control of the
    -- zone.  Otherwise the zone is contested and no team earns progress or
    -- points.
    local maxPlayers = 0
    local dominant = nil
    local teamPlayerCounts = {}

    -- Build a table of player counts per team and determine the dominant
    -- team (the one with the most players).  Ties are left unresolved;
    -- whichever team is encountered first with the highest count becomes
    -- dominant.  All teams with at least one player are included in
    -- teamPlayerCounts so the messages reflect every team in the zone.
    for team, count in pairs(kothZone.playersInZone) do
      if count > 0 then
        teamPlayerCounts[team] = count
        if count > maxPlayers then
          maxPlayers = count
          dominant = team
        end
      end
    end

    -- Determine if the dominant team has a clear majority.  If any other
    -- team has the same number of players as the dominant team, there is
    -- no majority and the zone should be contested.
    local hasMajority = true
    for team, count in pairs(teamPlayerCounts) do
      if team ~= dominant and count == maxPlayers then
        hasMajority = false
        break
      end
    end

    if hasMajority and dominant then
      -- One team has more players than all others.  They control the zone.
      if kothZone.controllingTeam ~= dominant then
        -- Zone control changed to a new team.  Clear contested state and
        -- reset progress.  Reset lastContestedCountStr so that a future
        -- contested state will announce properly.
        local wasContested = kothZone.isContested
        local previousController = kothZone.controllingTeam
        kothZone.controllingTeam = dominant
        kothZone.captureProgress = 0
        kothZone.dominantTeam = dominant
        kothZone.isContested = false
        kothZone.lastContestedCountStr = nil
        print(('[KOTH] Zone controlled by %s team with majority!'):format(dominant))
        TriggerClientEvent('koth:zoneControlChanged', -1, dominant)

        -- Build player count string for notification
        local countStr = ''
        for t, count in pairs(teamPlayerCounts) do
          if countStr ~= '' then countStr = countStr .. ' vs ' end
          countStr = countStr .. count .. ' ' .. t:upper()
        end
        -- Notify players with team colors
        local teamColors = {
          red = {255, 0, 0},
          green = {0, 255, 0},
          blue = {0, 100, 255}
        }
        local color = teamColors[dominant] or {255, 255, 255}
        local message = (wasContested and ('%s team has taken the zone! (%s)'):format(dominant:upper(), countStr))
                      or ('%s team has taken the zone! (%s)'):format(dominant:upper(), countStr)
        TriggerClientEvent('chat:addMessage', -1, {
          color = color,
          multiline = false,
          args = {'[ZONE]', message}
        })
      end
      -- Always set dominantTeam and clear contested flag
      kothZone.dominantTeam = dominant
      kothZone.isContested = false
    else
      -- No clear majority: the zone is contested.  Clear control, reset
      -- progress and update dominantTeam for UI colouring.
      local previousController = kothZone.controllingTeam
      kothZone.controllingTeam = nil
      kothZone.isContested = true
      kothZone.dominantTeam = dominant
      kothZone.captureProgress = 0
      -- If the zone was previously controlled, notify clients to update blip
      if previousController then
        print('[KOTH] Zone control cleared due to contesting')
        TriggerClientEvent('koth:zoneControlChanged', -1, nil)
      end
      -- Build contested composition string
      local countStr = ''
      for t, count in pairs(teamPlayerCounts) do
        if countStr ~= '' then countStr = countStr .. ' vs ' end
        countStr = countStr .. count .. ' ' .. t:upper()
      end
      -- Only broadcast if composition changed
      if kothZone.lastContestedCountStr ~= countStr then
        kothZone.lastContestedCountStr = countStr
        print('[KOTH] Zone contested by multiple teams!')
        TriggerClientEvent('chat:addMessage', -1, {
          color = {255, 255, 0},
          multiline = false,
          args = {'[ZONE]', ('Zone contested! (%s)'):format(countStr)}
        })
      end
    end
  end

  -- Broadcast zone status to all clients
  TriggerClientEvent('koth:updateZoneStatus', -1, {
    controllingTeam = kothZone.controllingTeam,
    captureProgress = kothZone.captureProgress,
    captureThreshold = kothZone.captureThreshold,
    dominantTeam = kothZone.dominantTeam,
    isContested = kothZone.isContested,
    playersInZone = kothZone.playersInZone
  })
end

-- KOTH capture processing loop
Citizen.CreateThread(function()
  -- Counter to control zone control bonus frequency.  We increment
  -- this counter each second and award the zone bonus only when it
  -- reaches the configured interval (in seconds).  Resetting the
  -- counter after each payout ensures that players receive the bonus
  -- at most once per interval.  The default interval is 90 seconds.
  local zoneBonusCounter = 0
  local zoneBonusInterval = 90 -- seconds between zone bonus payouts
  while true do
    Citizen.Wait(1000) -- Update every second

    -- Increment the zone bonus counter each second regardless of who controls the zone.
    -- When the counter reaches the configured interval, award a presence bonus to
    -- all players inside the zone.  Additionally, if the zone is
    -- controlled and not contested, award the standard control bonus to
    -- the controlling team.  After payouts the counter resets.
    zoneBonusCounter = zoneBonusCounter + 1
    if zoneBonusCounter >= zoneBonusInterval then
      -- Award control bonus only if there is a controlling team and the zone is not contested
      if kothZone.controllingTeam and not kothZone.isContested and not isRoundEnding then
        AwardZoneControlBonus(kothZone.controllingTeam)
      end
      -- Always award presence bonus to every player currently in the zone
      AwardZonePresenceBonus()
      zoneBonusCounter = 0
    end

    -- If the zone is controlled and not contested, increase capture progress
    if kothZone.controllingTeam and not kothZone.isContested and not isRoundEnding then
      kothZone.captureProgress = kothZone.captureProgress + kothZone.captureRate

      -- Award points when progress reaches threshold
      if kothZone.captureProgress >= kothZone.captureThreshold then
        kothZone.captureProgress = 0
        
        -- Update zone points
        zonePoints[kothZone.controllingTeam] = (zonePoints[kothZone.controllingTeam] or 0) + 1
        
        -- Broadcast updated points immediately
        TriggerClientEvent('koth:updateZonePoints', -1, zonePoints)
        
        print(('[KOTH] The %s team earned 1 point for controlling the zone! Total: %d'):format(
          kothZone.controllingTeam, zonePoints[kothZone.controllingTeam]))
        
        -- Notify point award with team color
        local teamColors = {
          red = {255, 0, 0},
          green = {0, 255, 0},
          blue = {0, 100, 255}
        }
        local color = teamColors[kothZone.controllingTeam] or {255, 255, 255}
        local pointMessage = ('%s team earned 1 point! (Total: %d)'):format(kothZone.controllingTeam:upper(), zonePoints[kothZone.controllingTeam])
        TriggerClientEvent('chat:addMessage', -1, {
          color = color,
          multiline = false,
          args = {'[ZONE]', pointMessage}
        })

        -- Check if this team has reached the winning score.  Use the
        -- POINTS_TO_WIN constant so the threshold can be adjusted easily.
        if zonePoints[kothZone.controllingTeam] >= POINTS_TO_WIN then
          if not isRoundEnding then
            isRoundEnding = true
            print(('[KOTH] The %s team reached %d points! Ending round...'):format(kothZone.controllingTeam, POINTS_TO_WIN))
            -- Award all members of the winning team with bonus XP and money
            AwardRoundWin(kothZone.controllingTeam)
            -- Notify clients that the round has ended and which team won
            TriggerClientEvent('koth:roundEnd', -1, kothZone.controllingTeam)
            -- Start a map vote after a short delay to allow the end‑round camera
            SetTimeout(10000, function()
              StartMapVote()
            end)
          end
        end
      end

      -- Always update zone status to show progress
      TriggerClientEvent('koth:updateZoneStatus', -1, {
        controllingTeam = kothZone.controllingTeam,
        captureProgress = kothZone.captureProgress,
        captureThreshold = kothZone.captureThreshold,
        dominantTeam = kothZone.dominantTeam,
        isContested = kothZone.isContested,
        playersInZone = kothZone.playersInZone
      })
    elseif kothZone.isContested then
      -- When contested, reset progress and show contested state
      if kothZone.captureProgress > 0 then
        kothZone.captureProgress = 0
      end
      
      -- Update clients about contested state
      TriggerClientEvent('koth:updateZoneStatus', -1, {
        controllingTeam = kothZone.controllingTeam,
        captureProgress = 0,
        captureThreshold = kothZone.captureThreshold,
        dominantTeam = nil,
        isContested = true,
        playersInZone = kothZone.playersInZone
      })
    end
  end
end)

--
-- Presence reward system
--
-- Award players XP and money periodically based on whether they are
-- currently inside the active KOTH capture zone.  Players inside the
-- zone receive 150 XP and $150 every interval, whereas players outside
-- the zone receive 50 XP and $50.  Rewards are applied to
-- playerData and pushed to clients via koth:updatePlayerData.  The
-- interval can be adjusted by changing the Wait duration below.
-- Presence reward system (tuned per admin request)
--
-- This thread awards XP and money only to players physically inside the
-- active capture zone, and only once every 90 seconds.  Players
-- outside the zone receive no passive rewards.  The reward amount
-- matches the zone control bonus (50 XP / $50), ensuring that
-- prolonged presence in the objective yields modest gains over time.
Citizen.CreateThread(function()
  local rewardInterval = 90000 -- 90 seconds between passive zone rewards
  while true do
    Citizen.Wait(rewardInterval)
    local zoneCfg = mapLocations[currentMapIndex]
    if zoneCfg and zoneCfg.zone then
      local zone = zoneCfg.zone
      local rSq = (zone.radius or 150.0) ^ 2
      for _, playerIdStr in ipairs(GetPlayers()) do
        local playerId = tonumber(playerIdStr)
        local ped = GetPlayerPed(playerId)
        if ped and DoesEntityExist(ped) then
          local coords = GetEntityCoords(ped)
          local dx, dy = coords.x - zone.x, coords.y - zone.y
          local inZone = (dx * dx + dy * dy) <= rSq
          -- Only award players inside the zone
          if inZone then
            local reward = 50
            local pdata = playerData[playerId]
            if pdata then
              pdata.money = (pdata.money or 0) + reward
              pdata.xp    = (pdata.xp or 0) + reward
              local newLevel = CalculateLevel(pdata.xp)
              if newLevel ~= pdata.level then
                pdata.level = newLevel
              end
              TriggerClientEvent('koth:updatePlayerData', playerId, pdata)
              print(('[KOTH] Presence reward: player %d inside zone, +$%d & +%d XP'):format(playerId, reward, reward))
            end
          end
        end
      end
    end
  end
end)

-- PVP SYSTEM SERVER-SIDE
AddEventHandler('playerConnecting', function()
  local source = source

  -- Enable PVP for connecting player
  Citizen.SetTimeout(5000, function() -- Wait for player to fully load
    TriggerClientEvent('koth:enablePVP', source)
  end)
end)

-- Enable PVP for all players when resource starts
AddEventHandler('onResourceStart', function(resourceName)
  if GetCurrentResourceName() ~= resourceName then return end

  print('[KOTH] Enabling PVP for all players...')

  -- Enable PVP for all currently connected players
  for _, playerId in ipairs(GetPlayers()) do
    TriggerClientEvent('koth:enablePVP', playerId)
  end
end)

-- PVP enable event
RegisterNetEvent('koth:enablePVP', function()
  local source = source
  print(('[KOTH] PVP enabled for player %d'):format(source))
end)

-- Debug command to check player stats
RegisterCommand('checkstats', function(source, args, rawCommand)
  if source == 0 then
    print('[KOTH] This command can only be used by players')
    return
  end

  if not playerData[source] then
    TriggerClientEvent('chat:addMessage', source, {
      color = {255, 0, 0},
      multiline = true,
      args = {"[KOTH]", "Player data not loaded! Try rejoining the server."}
    })
    print(('[KOTH] Player %d requested stats but data not loaded'):format(source))
    return
  end

  local data = playerData[source]
  TriggerClientEvent('chat:addMessage', source, {
    color = {0, 255, 0},
    multiline = true,
    args = {"[KOTH Stats]", string.format("Money: $%d | XP: %d | Level: %d | Kills: %d | Deaths: %d",
      data.money, data.xp, data.level, data.kills, data.deaths)}
  })

  print(('[KOTH] Player %d (%s) stats - Money: $%d | XP: %d | Level: %d'):format(source, data.player_name, data.money, data.xp, data.level))
end, false)

-- Calculate level from XP
function CalculateLevel(xp)
  local levels = {
    {level = 1, required = 0},
    {level = 2, required = 100},
    {level = 3, required = 250},
    {level = 4, required = 500},
    {level = 5, required = 1000},
    {level = 6, required = 1750},
    {level = 7, required = 2750},
    {level = 8, required = 4000},
    {level = 9, required = 6000},
    {level = 10, required = 8500},
    {level = 11, required = 11000},
    {level = 12, required = 14000},
    {level = 13, required = 17500},
    {level = 14, required = 21500},
    {level = 15, required = 26000},
    {level = 16, required = 31000},
    {level = 17, required = 36500},
    {level = 18, required = 42500},
    {level = 19, required = 49000},
    {level = 20, required = 56000},
    {level = 25, required = 100000},
    {level = 30, required = 150000},
    {level = 40, required = 250000},
    {level = 50, required = 400000}
  }

  local currentLevel = 1
  for _, levelData in ipairs(levels) do
    if xp >= levelData.required then
      currentLevel = levelData.level
    else
      break
    end
  end
  
  -- If XP is beyond level 50, calculate level based on formula
  if xp > 400000 then
    -- After level 50, each level requires 10000 more XP
    local extraXP = xp - 400000
    local extraLevels = math.floor(extraXP / 10000)
    currentLevel = 50 + extraLevels
  end

  return currentLevel
end

-- Award money and XP for kills - FIXED VERSION WITH EXTENSIVE DEBUG
function AwardKillReward(killerSource, victimSource, inZone)
  print('=== [KOTH SERVER] AWARD KILL REWARD START ===')
  print(('[KOTH] AwardKillReward called - Killer: %s (type: %s) | Victim: %s (type: %s) | In Zone: %s'):format(
    tostring(killerSource), type(killerSource), tostring(victimSource), type(victimSource), tostring(inZone)))

  -- Convert to number if needed (FiveM sometimes passes strings)
  local killerSourceNum = tonumber(killerSource)
  local victimSourceNum = tonumber(victimSource)

  print(('[KOTH] Converted IDs - Killer: %s | Victim: %s'):format(tostring(killerSourceNum), tostring(victimSourceNum)))

  -- Prevent self-kills
  if killerSourceNum == victimSourceNum then
    print(('[KOTH] Self-kill detected - no reward given'):format())
    return
  end

  if not playerData[killerSourceNum] then
    print(('[KOTH] CRITICAL ERROR: No player data found for killer %s - loading data...'):format(killerSourceNum))
    LoadPlayerData(killerSourceNum)
    -- Wait a moment for data to load
    Citizen.SetTimeout(1000, function()
      if playerData[killerSourceNum] then
        print(('[KOTH] Killer data loaded, retrying reward...'):format())
        AwardKillReward(killerSourceNum, victimSourceNum, inZone)
      else
        print(('[KOTH] FAILED to load killer data after retry!'):format())
      end
    end)
    return
  end

  if not playerData[victimSourceNum] then
    print(('[KOTH] CRITICAL ERROR: No player data found for victim %s'):format(victimSourceNum))
    return
  end

  -- Check if killer and victim are on different teams (with fallback)
  local killerTeam = GetPlayerTeam(killerSourceNum)
  local victimTeam = GetPlayerTeam(victimSourceNum)
  
  print(('[KOTH] Team check - Killer: %s team | Victim: %s team'):format(tostring(killerTeam), tostring(victimTeam)))
  
  -- If team data is missing, still allow the kill (better than blocking all kills)
  if killerTeam and victimTeam and killerTeam == victimTeam then
    print(('[KOTH] Teamkill detected - no reward given (both on %s team)'):format(killerTeam))
    return
  end

  print(('[KOTH] Processing kill reward - Valid opposite team kill'):format())

  -- Updated reward values as requested by the server owner:
  -- Out‑of‑zone kill (opposite team): $150 and 100 XP
  -- In‑zone kill (opposite team): $300 and 250 XP
  -- When a kill occurs inside the capture zone the player receives
  -- additional XP and money compared to kills outside of it.
  local baseXP = 100
  local baseMoney = 150
  -- If the kill happened in the zone, award 250 XP and $300; otherwise
  -- award the base amounts defined above.
  local xpReward = inZone and 250 or baseXP
  local moneyReward = inZone and 300 or baseMoney

  -- Apply a VIP multiplier for players who possess the configured role.
  -- A 1.5x multiplier has been requested by the server owner.  When
  -- enabled, both XP and money rewards are increased proportionally.
  if IsPlayerVIP(killerSourceNum) then
    xpReward = math.floor(xpReward * 1.5)
    moneyReward = math.floor(moneyReward * 1.5)
  end

  print(('[KOTH] Reward calculation - Base XP: %d | Base Money: $%d | Zone Kill: %s'):format(baseXP, baseMoney, tostring(inZone)))
  print(('[KOTH] Final rewards - XP: %d | Money: $%d'):format(xpReward, moneyReward))

  -- Update killer stats (use converted numbers)
  local killerData = playerData[killerSourceNum]
  local oldLevel = killerData.level
  local oldMoney = killerData.money
  local oldXP = killerData.xp
  local oldKills = killerData.kills

  print(('[KOTH] Killer BEFORE - Money: $%d | XP: %d | Level: %d | Kills: %d'):format(
    oldMoney, oldXP, oldLevel, oldKills))

  killerData.money = killerData.money + moneyReward
  killerData.xp = killerData.xp + xpReward
  killerData.kills = killerData.kills + 1

  if inZone then
    killerData.zone_kills = killerData.zone_kills + 1
    print(('[KOTH] Zone kill recorded - new zone_kills: %d'):format(killerData.zone_kills))
  end

  -- Calculate new level
  killerData.level = CalculateLevel(killerData.xp)

  print(('[KOTH] Killer AFTER - Money: $%d | XP: %d | Level: %d | Kills: %d'):format(
    killerData.money, killerData.xp, killerData.level, killerData.kills))

  -- Update victim stats (use converted numbers)
  local victimData = playerData[victimSourceNum]
  local oldDeaths = victimData.deaths
  victimData.deaths = victimData.deaths + 1

  print(('[KOTH] Victim deaths updated from %d to %d'):format(oldDeaths, victimData.deaths))

  -- Prepare reward data for client
  local rewardData = {
    xp = xpReward,
    money = moneyReward,
    inZone = inZone,
    victimName = GetPlayerName(victimSourceNum),
    killerTeam = killerTeam,
    victimTeam = victimTeam
  }

  print('=== [KOTH SERVER] SENDING CLIENT EVENTS ===')
  print(('[KOTH] Sending kill reward to killer %d: %s'):format(killerSourceNum, json.encode(rewardData)))

  -- Send enhanced reward notification to killer (use converted numbers)
  TriggerClientEvent('koth:showKillReward', killerSourceNum, rewardData)

  -- Check for level up
  if killerData.level > oldLevel then
    local levelUpData = {
      newLevel = killerData.level,
      oldLevel = oldLevel
    }
    print(('[KOTH] Sending level up to killer %d: %s'):format(killerSourceNum, json.encode(levelUpData)))
    TriggerClientEvent('koth:levelUp', killerSourceNum, levelUpData)
  end

  print(('[KOTH] Sending updated player data to killer %d'):format(killerSourceNum))
  print(('[KOTH] Killer data being sent: Money=$%d, XP=%d, Level=%d'):format(
    killerData.money, killerData.xp, killerData.level))

  -- Update client data (use converted numbers) - FORCE UPDATE
  -- Refresh VIP status for killer and victim before sending.  This
  -- ensures the client displays the correct VIP multiplier indicator
  -- even if the player's Discord role changed mid‑session.
  killerData.isVip = IsPlayerVIP(killerSourceNum)
  victimData.isVip = IsPlayerVIP(victimSourceNum)

  TriggerClientEvent('koth:updatePlayerData', killerSourceNum, killerData)

  print(('[KOTH] Sending updated player data to victim %d'):format(victimSourceNum))
  TriggerClientEvent('koth:updatePlayerData', victimSourceNum, victimData)

  print('=== [KOTH SERVER] SAVING TO DATABASE ===')
  -- Save data (use converted numbers)
  SavePlayerData(killerSourceNum)
  SavePlayerData(victimSourceNum)

  print('=== [KOTH SERVER] KILL REWARD COMPLETE ===')
  print(('[KOTH] FINAL SUMMARY: %s (%s) killed %s (%s) | XP: +%d | Money: +$%d | Zone: %s'):format(
    GetPlayerName(killerSourceNum),
    tostring(killerTeam),
    GetPlayerName(victimSourceNum),
    tostring(victimTeam),
    xpReward,
    moneyReward,
    inZone and 'YES' or 'NO'
  ))
  print('=== [KOTH SERVER] AWARD KILL REWARD END ===')
end

-- Kill detection event
RegisterNetEvent('koth:playerKilled', function(killerSource, victimSource, inZone)
  print('=== [KOTH SERVER] KILL EVENT RECEIVED ===')
  print(('[KOTH] Raw data - Killer: %s (type: %s) | Victim: %s (type: %s) | In Zone: %s'):format(
    tostring(killerSource), type(killerSource), tostring(victimSource), type(victimSource), tostring(inZone)))

  -- Get player names for debugging
  local killerName = 'Unknown'
  local victimName = 'Unknown'
  
  if killerSource and tonumber(killerSource) then
    killerName = GetPlayerName(tonumber(killerSource)) or 'Unknown'
  end
  if victimSource and tonumber(victimSource) then
    victimName = GetPlayerName(tonumber(victimSource)) or 'Unknown'
  end
  
  print(('[KOTH] Player names - Killer: %s | Victim: %s'):format(killerName, victimName))

  -- Validate killer and victim IDs
  local killerIdNum = tonumber(killerSource)
  local victimIdNum = tonumber(victimSource)

  print(('[KOTH] Converted IDs - Killer: %s | Victim: %s'):format(tostring(killerIdNum), tostring(victimIdNum)))

  if killerIdNum and victimIdNum and killerIdNum ~= victimIdNum then
    -- Check if killer and victim are connected players
    local killerPlayer = GetPlayerPed(killerIdNum)
    local victimPlayer = GetPlayerPed(victimIdNum)
    
    print(('[KOTH] Player peds - Killer: %s | Victim: %s'):format(tostring(killerPlayer), tostring(victimPlayer)))

    if killerPlayer and killerPlayer ~= 0 and victimPlayer and victimPlayer ~= 0 then
      print('=== [KOTH SERVER] CHECKING PLAYER DATA ===')
      
      -- Check if player data exists
      local killerHasData = playerData[killerIdNum] ~= nil
      local victimHasData = playerData[victimIdNum] ~= nil
      
      print(('[KOTH] Player data exists - Killer: %s | Victim: %s'):format(tostring(killerHasData), tostring(victimHasData)))
      
      if killerHasData then
        local kData = playerData[killerIdNum]
        print(('[KOTH] Killer data - Money: $%s | XP: %s | Level: %s | TXID: %s'):format(
          tostring(kData.money), tostring(kData.xp), tostring(kData.level), tostring(kData.txid)))
      else
        print('[KOTH] ERROR: Killer has no player data! Attempting to load...')
        LoadPlayerData(killerIdNum)
        
        -- Wait and retry
        Citizen.SetTimeout(1000, function()
          if playerData[killerIdNum] then
            print('[KOTH] Killer data loaded successfully, retrying kill reward...')
            TriggerEvent('koth:playerKilled', killerSource, victimSource, inZone)
          else
            print('[KOTH] CRITICAL ERROR: Could not load killer data!')
          end
        end)
        return
      end
      
      if victimHasData then
        local vData = playerData[victimIdNum]
        print(('[KOTH] Victim data - Money: $%s | XP: %s | Level: %s | TXID: %s'):format(
          tostring(vData.money), tostring(vData.xp), tostring(vData.level), tostring(vData.txid)))
      else
        print('[KOTH] ERROR: Victim has no player data! Attempting to load...')
        LoadPlayerData(victimIdNum)
        
        -- Wait and retry
        Citizen.SetTimeout(1000, function()
          if playerData[victimIdNum] then
            print('[KOTH] Victim data loaded successfully, retrying kill reward...')
            TriggerEvent('koth:playerKilled', killerSource, victimSource, inZone)
          else
            print('[KOTH] CRITICAL ERROR: Could not load victim data!')
          end
        end)
        return
      end
      
      print('=== [KOTH SERVER] PROCESSING KILL REWARD ===')
      AwardKillReward(killerIdNum, victimIdNum, inZone)

      -- Update leaderboard statistics.  Increment the killer's entry in the
      -- roundKills table.  This table tracks kills only for the current
      -- round/map and is cleared when the map rotates.  Without this
      -- update the leaderboard would not reflect the most recent kill.
      if killerIdNum then
        roundKills[killerIdNum] = (roundKills[killerIdNum] or 0) + 1
      end

      -- Award XP for the killer's currently selected class.  This XP
      -- contributes towards class-level progression.  The amount can be
      -- tuned via configuration or scaled based on kill type; here we
      -- Award XP for the killer's currently selected class.  The
      -- amount depends on whether the kill occurred inside the zone
      -- or outside of it.  Per the server owner's request, kills
      -- outside the zone grant 50 class XP, while kills in the zone
      -- grant 250 class XP.  Adjust these values here if you wish
      -- to change the class progression rate.
      local classXP = inZone and 250 or 50
      TriggerEvent('koth_classes:addClassXP', killerIdNum, classXP)

      -- After awarding class XP, refresh the killer's class level.  The
      -- refresh ensures that any level‑ups resulting from the XP
      -- grant are reflected when we send updated player data later
      -- in this event handler.
      refreshPlayerClassLevel(killerIdNum)
      
      -- TRIGGER DAILY CHALLENGE EVENTS
      print('=== [KOTH SERVER] UPDATING DAILY CHALLENGES ===')
      
      -- Track kill for killstreak challenge (specify the killer source)
      TriggerEvent('leaderboard:playerKill', killerIdNum)
      
      -- Track zone kill if applicable
      if inZone then
        print('[KOTH] Triggering zone kill challenge update for player ' .. killerIdNum)
        TriggerEvent('leaderboard:zoneKill', killerIdNum)
      end
      
      -- Track death for victim (resets killstreak)
      TriggerEvent('leaderboard:playerDied', victimIdNum)
      
      print('[KOTH] Daily challenge events triggered for killer: ' .. killerIdNum .. ' and victim: ' .. victimIdNum)

      -- Send a kill feed notification to all players.  Instead of
      -- broadcasting a single formatted string, pass a structured
      -- table containing the killer/victim names and teams.  This
      -- allows the client UI to display the message in a custom
      -- element rather than using the native feed ticker.  See the
      -- client.lua handler for 'koth:killFeed' for details.
      local killerTeam = GetPlayerTeam(killerIdNum)
      local victimTeam = GetPlayerTeam(victimIdNum)
      TriggerClientEvent('koth:killFeed', -1, {
        killerName = killerName or 'Unknown',
        killerTeam = killerTeam or 'neutral',
        victimName = victimName or 'Unknown',
        victimTeam = victimTeam or 'neutral'
      })
    else
      print('[KOTH] Kill event ignored - killer or victim ped invalid')
      print(('[KOTH] Killer ped: %s | Victim ped: %s'):format(tostring(killerPlayer), tostring(victimPlayer)))
    end
  else
    print('[KOTH] Kill event ignored - invalid data or self-kill')
    print(('[KOTH] Killer ID: %s | Victim ID: %s | Same player: %s'):format(
      tostring(killerIdNum), tostring(victimIdNum), tostring(killerIdNum == victimIdNum)))
  end
  
  print('=== [KOTH SERVER] KILL EVENT PROCESSING COMPLETE ===')
end)

--[[
  Debug command to print server stats.  Disabled to prevent exposing
  server internals to players.
]]
-- RegisterCommand('serverstats', function(source)
--   if source == 0 then
--     print('[KOTH] Server player data:')
--     for playerId, data in pairs(playerData) do
--       print(('  Player %d: Money=$%d, Level=%d, XP=%d'):format(
--         playerId, data.money or 0, data.level or 0, data.xp or 0))
--     end
--   else
--     if playerData[source] then
--       print(('[KOTH] Player %d data - Money: $%d, Level: %d, XP: %d'):format(
--         source, playerData[source].money or 0, playerData[source].level or 0, playerData[source].xp or 0))
--     else
--       print(('[KOTH] No data found for player %d'):format(source))
--     end
--   end
-- end, true)

--[[
  Debug command to open the attachment menu with test data.  Disabled for players.
]]
-- RegisterCommand('testattachments', function(source)
--   if source == 0 then
--     print('[KOTH] This command must be used by a player')
--     return
--   end
--   
--   -- Simulate having a weapon equipped
--   local testWeaponHash = GetHashKey('WEAPON_CARBINERIFLE')
--   
--   print(('[KOTH] Testing attachment menu for player %d'):format(source))
--   
--   -- Trigger the attachment menu event
--   TriggerEvent('koth:getAttachmentMenu', testWeaponHash)
--   
--   -- Also trigger it directly to ensure it works
--   local money = 0
--   if playerData[source] and playerData[source].money then
--     money = playerData[source].money
--   end

--   -- Get weapon-specific attachments or use defaults
--   local weaponName = "WEAPON_CARBINERIFLE"
--   local attachments = weaponAttachments[weaponName] or defaultAttachments
--   
--   -- Prepare attachment list with price and full image path
--   local attachmentList = {}
--   for _, attachment in ipairs(attachments) do
--     table.insert(attachmentList, {
--       name = attachment.name,
--       component = attachment.component,
--       price = attachmentPrice,
--       image = "nui://koth_teamsel/images of guns/" .. attachment.image  -- Use NUI protocol for images
--     })
--   end

--   -- Send attachment menu data to client
--   TriggerClientEvent('koth:showAttachmentMenu', source, {
--     attachments = attachmentList,
--     weaponName = "Carbine Rifle",
--     money = money
--   })
--   
--   print(('[KOTH] Sent test attachment menu to player %d'):format(source))
-- end, false)

--[[
  Comprehensive test command for kill rewards.  Disabled for production.
]]
-- RegisterCommand('testkill', function(source, args)
--[[
  if source == 0 then
    print('[KOTH] This command must be used by a player')
    return
  end
  
  local inZone = args[1] == 'zone' or args[1] == 'true'
  local xpReward = inZone and 150 or 50
  local moneyReward = inZone and 150 or 50
  
  print('=== [KOTH SERVER] TEST KILL COMMAND START ===')
  print(('[KOTH] Testing kill reward for player %d - Zone: %s, XP: %d, Money: $%d'):format(source, tostring(inZone), xpReward, moneyReward))
  
  -- Check if player data exists
  if not playerData[source] then
    print('[KOTH] ERROR: Player data not found, loading...')
    LoadPlayerData(source)
    Citizen.SetTimeout(1000, function()
      if playerData[source] then
        print('[KOTH] Player data loaded, retrying test...')
        TriggerEvent('testkill', source, args)
      else
        print('[KOTH] FAILED to load player data for test!')
      end
    end)
    return
  end
  
  print('[KOTH] Player data found, proceeding with test...')
  
  -- Send kill reward directly to test UI
  local rewardData = {
    xp = xpReward,
    money = moneyReward,
    inZone = inZone,
    victimName = 'Test Player'
  }
  
  print('[KOTH] Sending kill reward to client:', json.encode(rewardData))
  TriggerClientEvent('koth:showKillReward', source, rewardData)
  
  -- Also update player money and XP for testing
  local oldMoney = playerData[source].money or 1000
  local oldXP = playerData[source].xp or 0
  
  playerData[source].money = oldMoney + moneyReward
  playerData[source].xp = oldXP + xpReward
  playerData[source].kills = (playerData[source].kills or 0) + 1
  
  print('[KOTH] Updated player stats - Money: $' .. oldMoney .. ' -> $' .. playerData[source].money .. ', XP: ' .. oldXP .. ' -> ' .. playerData[source].xp)
  
  -- Update client HUD
  print('[KOTH] Sending updated player data to client:', json.encode(playerData[source]))
  TriggerClientEvent('koth:updatePlayerData', source, playerData[source])
  SavePlayerData(source)
  
  print('[KOTH] Test kill reward complete!')
  print('=== [KOTH SERVER] TEST KILL COMMAND END ===')
--]]
-- end, false)

--[[
  Debug command to simulate a kill.  Disabled for production.
]]
-- RegisterCommand('simulatekill', function(source, args)
--[[
  if source == 0 then
    print('[KOTH] This command must be used by a player')
    return
  end
  
  local inZone = args[1] == 'zone' or args[1] == 'true'
  
  print(('[KOTH] Simulating kill for player %d (in zone: %s)'):format(source, tostring(inZone)))
  
  -- Simulate the player killing themselves for testing
  TriggerEvent('koth:playerKilled', source, source, inZone)
  
  -- Also trigger the reward directly to test the client event
  Citizen.SetTimeout(500, function()
    local xpReward = inZone and 150 or 50
    local moneyReward = inZone and 150 or 50
    
    TriggerClientEvent('koth:showKillReward', source, {
      xp = xpReward,
      money = moneyReward,
      inZone = inZone,
      victimName = 'Test Victim'
    })
    
    print(('[KOTH] Sent kill reward to player %d - XP: %d, Money: $%d'):format(source, xpReward, moneyReward))
  end)
--]]
-- end, false)

--[[
  Debug commands to manually load and inspect player data.  Disabled.
]]
-- RegisterCommand('loaddata', function(source)
--   print(('[KOTH] Manually loading data for %s'):format(GetPlayerName(source)))
--   LoadPlayerData(source)
-- end, false)

-- RegisterCommand('checkdata', function(source)
--   print(('[KOTH] Player data for %s: %s'):format(GetPlayerName(source), json.encode(playerData[source] or 'nil')))
--   print(('[KOTH] playerData table type: %s'):format(type(playerData)))
--   for k, v in pairs(playerData) do
--     print(('[KOTH] playerData[%s] = %s'):format(k, json.encode(v)))
--   end
-- end, false)

-- ADMIN PANEL INTEGRATION - Export functions for admin panel to use
exports('GivePlayerMoney', function(playerId, amount)
  local source = tonumber(playerId)
  if not source or not playerData[source] then
    print(('[KOTH] GivePlayerMoney: Invalid player ID %s or no cached data'):format(tostring(playerId)))
    return false
  end
  
  -- Update cached data
  playerData[source].money = (playerData[source].money or 0) + amount
  
  -- Save to database
  SavePlayerData(source)
  
  -- Update client HUD immediately
  TriggerClientEvent('koth:updatePlayerData', source, playerData[source])
  
  print(('[KOTH] Admin gave $%d to player %d - new balance: $%d'):format(amount, source, playerData[source].money))
  return true
end)

exports('GivePlayerXP', function(playerId, amount)
  local source = tonumber(playerId)
  if not source or not playerData[source] then
    print(('[KOTH] GivePlayerXP: Invalid player ID %s or no cached data'):format(tostring(playerId)))
    return false
  end
  
  -- Update cached data
  local oldLevel = playerData[source].level
  playerData[source].xp = (playerData[source].xp or 0) + amount
  playerData[source].level = CalculateLevel(playerData[source].xp)
  
  -- Save to database
  SavePlayerData(source)
  
  -- Check for level up
  if playerData[source].level > oldLevel then
    TriggerClientEvent('koth:levelUp', source, {
      newLevel = playerData[source].level,
      oldLevel = oldLevel
    })
  end
  
  -- Update client HUD immediately
  TriggerClientEvent('koth:updatePlayerData', source, playerData[source])
  
  print(('[KOTH] Admin gave %d XP to player %d - new XP: %d, level: %d'):format(amount, source, playerData[source].xp, playerData[source].level))
  return true
end)

exports('SetPlayerLevel', function(playerId, level)
  local source = tonumber(playerId)
  if not source or not playerData[source] then
    print(('[KOTH] SetPlayerLevel: Invalid player ID %s or no cached data'):format(tostring(playerId)))
    return false
  end
  
  -- Calculate XP for the new level
  local levels = {
    {level = 1, required = 0},
    {level = 2, required = 100},
    {level = 3, required = 250},
    {level = 4, required = 500},
    {level = 5, required = 1000},
    {level = 6, required = 1750},
    {level = 7, required = 2750},
    {level = 8, required = 4000},
    {level = 9, required = 6000},
    {level = 10, required = 8500},
    {level = 11, required = 11000},
    {level = 12, required = 14000},
    {level = 13, required = 17500},
    {level = 14, required = 21500},
    {level = 15, required = 26000},
    {level = 16, required = 31000},
    {level = 17, required = 36500},
    {level = 18, required = 42500},
    {level = 19, required = 49000},
    {level = 20, required = 56000},
    {level = 25, required = 100000},
    {level = 30, required = 150000},
    {level = 40, required = 250000},
    {level = 50, required = 400000}
  }
  
  local requiredXP = 0
  for _, levelData in ipairs(levels) do
    if levelData.level == level then
      requiredXP = levelData.required
      break
    end
  end
  
  -- If level not in table, calculate XP based on formula
  if requiredXP == 0 and level > 10 then
    -- Formula: each level after 10 requires 2500 more XP than previous
    requiredXP = 8500 + ((level - 10) * 2500)
  end
  
  -- Update cached data
  local oldLevel = playerData[source].level
  playerData[source].level = level
  playerData[source].xp = requiredXP
  
  -- Save to database
  SavePlayerData(source)
  
  -- Check for level up notification
  if level > oldLevel then
    TriggerClientEvent('koth:levelUp', source, {
      newLevel = level,
      oldLevel = oldLevel
    })
  end
  
  -- Update client HUD immediately
  TriggerClientEvent('koth:updatePlayerData', source, playerData[source])
  
  print(('[KOTH] Admin set player %d to level %d (XP: %d)'):format(source, level, requiredXP))
  return true
end)

exports('ResetPlayerStats', function(playerId)
  local source = tonumber(playerId)
  if not source or not playerData[source] then
    print(('[KOTH] ResetPlayerStats: Invalid player ID %s or no cached data'):format(tostring(playerId)))
    return false
  end
  
  -- Reset cached data to defaults
  playerData[source].money = 1000
  playerData[source].xp = 0
  playerData[source].level = 1
  playerData[source].kills = 0
  playerData[source].deaths = 0
  playerData[source].zone_kills = 0
  
  -- Save to database
  SavePlayerData(source)
  
  -- Update client HUD immediately
  TriggerClientEvent('koth:updatePlayerData', source, playerData[source])
  
  print(('[KOTH] Admin reset stats for player %d'):format(source))
  return true
end)

--[[
  Reset a player's prestige progress.  This function sets the
  player's prestige rank and token counts back to zero without
  affecting their owned prestige items.  It is intended for use by
  administrative tools to allow a player to re‑grind prestige levels
  from scratch.  Returns true on success or false if the player is
  invalid or not loaded.  The player's data is saved and their HUD
  updated.

  Parameters:
    playerId (number|string): The server ID of the player to reset
]]
exports('ResetPlayerPrestige', function(playerId)
  local source = tonumber(playerId)
  if not source or not playerData[source] then
    print(('[KOTH] ResetPlayerPrestige: Invalid player ID %s or no cached data'):format(tostring(playerId)))
    return false
  end
  -- Reset prestige rank and tokens
  playerData[source].prestigeRank = 0
  playerData[source].prestigeWeaponTokens = 0
  playerData[source].prestigeVehicleTokens = 0
  -- Persist changes
  SavePlayerData(source)
  -- Update client HUD immediately
  TriggerClientEvent('koth:updatePlayerData', source, playerData[source])
  print(('[KOTH] Admin reset prestige for player %d'):format(source))
  return true
end)

--[[
  Force the current round to end immediately and load a new random map.
  This can be invoked via the admin panel or a console command.  The
  function simulates a team reaching the score limit by setting
  isRoundEnding to true, broadcasting the round end event to all
  clients, waiting a few seconds to allow the end‑round camera to
  display and then invoking RotateMap() which chooses a random map.

  Parameters:
    winTeam (string|nil): Optional team identifier ('red', 'green' or
                          'blue') to display as the winner.  If nil,
                          the currently controlling team is used or
                          defaults to 'red' when none.
]]
local function ForceNextMap(winTeam)
  if isRoundEnding then
    print('[KOTH] ForceNextMap called but a round end is already in progress')
    return false
  end
  isRoundEnding = true
  -- Determine which team will be announced as the winner.  Prefer the
  -- controlling team if not explicitly provided.
  local team = winTeam
  if not team then
    team = kothZone and kothZone.controllingTeam or nil
  end
  if not team then team = 'red' end
  -- Set the zone points for the winning team to the threshold to
  -- visually reflect the victory if any UI relies on this value
  zonePoints[team] = POINTS_TO_WIN
  -- Notify all clients that the round has ended
  TriggerClientEvent('koth:roundEnd', -1, team)
  print(('[KOTH] ForceNextMap triggered by admin - declaring %s the winner'):format(team))
  -- Award the specified team (or controlling team) with victory bonuses
  AwardRoundWin(team)
  -- After a short delay start a map vote for the next map instead of
  -- immediately rotating to a random one.  This gives players an
  -- opportunity to choose the next location even during forced
  -- transitions.
  SetTimeout(10000, function()
    StartMapVote()
  end)
  return true
end

-- Export function for admin panel scripts to force the next map.  When
-- called, this will immediately end the current round and rotate to a
-- new random map.  Optionally specify which team should be shown as
-- the winner; otherwise the controlling team or 'red' is used.
exports('ForceNextMap', function(winTeam)
  return ForceNextMap(winTeam)
end)

--[[
  Force a player onto a specific team.  This bypasses the normal team
  balancing logic used during voluntary team selection and allows an
  administrator to override a player's team assignment.  When a
  player is forcibly moved, they will be spawned at the new team's
  spawn point and team counts will be updated.  Returns true on
  success or false if the team is invalid.  This function is
  exported so that other resources (e.g. the admin panel) can call
  it directly.
]]
local function ForcePlayerTeam(targetId, team)
  if not team or not teamSpawns[team] then
    print(('[KOTH] ForcePlayerTeam: invalid team %s'):format(tostring(team)))
    return false
  end
  local tid = tonumber(targetId)
  if not tid then
    print(('[KOTH] ForcePlayerTeam: invalid target id %s'):format(tostring(targetId)))
    return false
  end
  -- Decrement the count for the player's current team, if any
  local oldTeam = playerTeams[tid]
  if oldTeam and teamCounts[oldTeam] then
    teamCounts[oldTeam] = teamCounts[oldTeam] - 1
    if teamCounts[oldTeam] < 0 then teamCounts[oldTeam] = 0 end
  end
  -- Assign the new team and increment its count
  playerTeams[tid] = team
  teamCounts[team] = (teamCounts[team] or 0) + 1
  print(('[KOTH] ForcePlayerTeam: player %d moved to team %s'):format(tid, team))
  -- Spawn the player at their new team's spawn location
  local spawn = teamSpawns[team]
  if spawn then
    TriggerClientEvent('koth:spawnPlayer', tid, spawn)
  end
  -- Update counts and zone points for all clients
  UpdateTeamCounts()
  UpdateZonePoints()
  -- Update the player's UI data if loaded
  if playerData[tid] then
    TriggerClientEvent('koth:updatePlayerData', tid, playerData[tid])
  end
  return true
end

-- Export the ForcePlayerTeam function so other resources can call it
exports('ForcePlayerTeam', function(targetId, team)
  return ForcePlayerTeam(targetId, team)
end)

-- Admin/console command to force the next map.  Usage: /nextmap
RegisterCommand('nextmap', function(source, args, raw)
  local src = source
  -- Only console or players with koth.admin ACE may use this command
  if src ~= 0 and not IsPlayerAceAllowed(src, 'koth.admin') then
    TriggerClientEvent('chat:addMessage', src, {
      color = {255, 0, 0},
      multiline = true,
      args = {"[KOTH]", "You do not have permission to use this command."}
    })
    return
  end
  -- Optional argument: winning team
  local teamArg = args[1] and tostring(args[1]):lower() or nil
  local validTeams = { red = true, green = true, blue = true }
  if teamArg and not validTeams[teamArg] then
    TriggerClientEvent('chat:addMessage', src, {
      color = {255, 0, 0},
      multiline = true,
      args = {"[KOTH]", "Invalid team specified. Use red, green or blue."}
    })
    return
  end
  -- Force the next map
  local ok = ForceNextMap(teamArg)
  if ok and src ~= 0 then
    TriggerClientEvent('chat:addMessage', src, {
      color = {0, 255, 0},
      multiline = true,
      args = {"[KOTH]", "Next map command executed"}
    })
  end
end, false)

-- Event to handle admin panel money/XP updates
RegisterNetEvent('koth:adminUpdatePlayerData', function(playerId, newData)
  local source = tonumber(playerId)
  if not source or not playerData[source] then
    print(('[KOTH] adminUpdatePlayerData: Invalid player ID %s or no cached data'):format(tostring(playerId)))
    return
  end
  
  -- Update cached data with new values
  if newData.money then
    playerData[source].money = newData.money
  end
  if newData.xp then
    local oldLevel = playerData[source].level
    playerData[source].xp = newData.xp
    playerData[source].level = newData.level or CalculateLevel(newData.xp)
    
    -- Check for level up
    if playerData[source].level > oldLevel then
      TriggerClientEvent('koth:levelUp', source, {
        newLevel = playerData[source].level,
        oldLevel = oldLevel
      })
    end
  end
  if newData.level then
    playerData[source].level = newData.level
  end
  
  -- Save to database
  SavePlayerData(source)
  
  -- Update client HUD immediately
  TriggerClientEvent('koth:updatePlayerData', source, playerData[source])
  
  print(('[KOTH] Admin updated player %d data via event'):format(source))
end)

--[[
  Provide a leaderboard of the top players for the current KOTH round.
  When a client presses the Tab key (handled on the client side) it
  triggers 'koth:requestLeaderboard'.  We then iterate through the
  roundKills table, collect each player's name, team and kill count,
  sort the results by kill count descending and return the top 15
  entries.  If a player's name cannot be determined via GetPlayerName
  (for example if the player disconnects) we fall back to using the
  cached playerData entry.  The resulting list is sent only to the
  requesting client via 'koth:receiveLeaderboard'.
]]
RegisterNetEvent('koth:requestLeaderboard', function()
  local src = source
  local leaderboard = {}
  -- Build a list of players and their kill counts for this round
  for pid, kills in pairs(roundKills) do
    local idNum = tonumber(pid)
    if idNum and kills and kills > 0 then
      local playerName = GetPlayerName(idNum) or nil
      -- Fall back to cached data if name is nil or empty
      if not playerName or playerName == '' then
        local pdata = playerData[idNum]
        playerName = pdata and pdata.player_name or 'Unknown'
      end
      -- Determine the player's team for colouring on the client
      local team = GetPlayerTeam(idNum) or 'neutral'
      table.insert(leaderboard, { name = playerName, kills = kills, team = team })
    end
  end
  -- Sort by kills descending
  table.sort(leaderboard, function(a, b)
    return (a.kills or 0) > (b.kills or 0)
  end)
  -- Trim to the top 15 entries
  local topList = {}
  local limit = math.min(15, #leaderboard)
  for i = 1, limit do
    topList[i] = leaderboard[i]
  end
  -- Send the leaderboard to the requesting client
  TriggerClientEvent('koth:receiveLeaderboard', src, topList)
end)

--[[
  Temporary test command for triggering a team win.  Disabled for production.
]]
-- RegisterCommand('testteamwin', function(source, args)
--[[
  if source == 0 then
    print('[KOTH] This command must be used by a player')
    return
  end
  
  local playerTeam = GetPlayerTeam(source)
  if not playerTeam then
    print('[KOTH] Player has no team assigned')
    return
  end
  
  -- Trigger team win for player's team
  TriggerEvent('leaderboard:teamWin', playerTeam)
  
  TriggerClientEvent('chat:addMessage', source, {
    color = {0, 255, 0},
    multiline = false,
    args = {'[KOTH]', 'Simulated team win for ' .. playerTeam .. ' team (for testing daily challenges)'}
  })
  
  print(('[KOTH] Triggered team win for %s team (test command by player %d)'):format(playerTeam, source))
--]]
-- end, false)

print('[KOTH] Server loaded successfully')

--
-- MEDIC REVIVE HANDLER
--
-- When a client emits the koth:revivePlayer event the server
-- verifies that the player attempting the revive (source) and the
-- target (targetServerId) are on the same team.  If they are
-- teammates the server triggers a client-side revival event on the
-- target.  Otherwise the revive request is ignored.  This simple
-- validation prevents medics from reviving members of other teams.

RegisterNetEvent('koth:revivePlayer')
AddEventHandler('koth:revivePlayer', function(targetServerId)
  local src = source
  local targetId = tonumber(targetServerId)
  if not targetId or targetId <= 0 then
    print(('[KOTH] Revive failed: invalid target id %s from %s'):format(tostring(targetServerId), tostring(src)))
    return
  end
  -- Fetch team assignments for both players
  local srcTeam = playerTeams[src]
  local targetTeam = playerTeams[targetId]
  if not srcTeam or not targetTeam then
    print(('[KOTH] Revive failed: teams missing for source %s or target %s'):format(tostring(src), tostring(targetId)))
    return
  end
  -- Only allow revive if both players share the same team
  if srcTeam ~= targetTeam then
    print(('[KOTH] Revive denied: %s (team %s) attempted to revive %s (team %s)'):format(src, srcTeam, targetId, targetTeam))
    return
  end
  -- Trigger the revive on the target client
  TriggerClientEvent('koth:reviveClient', targetId)
  print(('[KOTH] Revive triggered: %s revived %s on team %s'):format(src, targetId, srcTeam))
end)

-- =========================================================================
-- DOWNED PLAYER BROADCASTING
-- =========================================================================
-- These events notify all clients when a player becomes downed (eligible for
-- revival by a medic) and when they are revived or respawned.  Clients use
-- this to maintain a list of downed players instead of relying on native
-- functions like IsEntityDead, which may not return true when a player is
-- ragdolled by our custom death system.  When a player dies, their client
-- triggers koth:playerDowned with their server ID.  The server then
-- broadcasts koth:addDownedPlayer to all clients.  When the player is
-- revived, their client triggers koth:playerRevived and the server
-- broadcasts koth:removeDownedPlayer.

-- Broadcast to all clients that a player is downed
RegisterNetEvent('koth:playerDowned')
AddEventHandler('koth:playerDowned', function(serverId)
  local src = source
  local id = tonumber(serverId)
  if not id or id <= 0 then return end
  -- Forward to all clients except the downed player themselves.  The
  -- downed player does not need to track themselves in the downed list.
  TriggerClientEvent('koth:addDownedPlayer', -1, id)
end)

-- Broadcast to all clients that a player has been revived (or respawned)
RegisterNetEvent('koth:playerRevived')
AddEventHandler('koth:playerRevived', function(serverId)
  local src = source
  local id = tonumber(serverId)
  if not id or id <= 0 then return end
  TriggerClientEvent('koth:removeDownedPlayer', -1, id)
end)

--[[
  Chat restriction: Prevent non‑admin players from sending plain
  chat messages.  Commands (messages starting with '/') are
  unaffected.  Players with the ACE `koth.chat` can bypass this
  restriction.  Others attempting to chat will see a system
  notification and the message will be cancelled.
]]
AddEventHandler('chatMessage', function(source, name, msg)
  -- Trim leading/trailing whitespace for reliable prefix checking
  local trimmed = msg and msg:match('^%s*(.-)%s*$') or ''
  -- Block all chat commands.  Any message beginning with '/' is
  -- considered a command.  We cancel the event and inform the
  -- player that commands have been disabled.  Returning here
  -- prevents any subsequent chat handlers from processing the
  -- command.
  if trimmed:sub(1, 1) == '/' then
    TriggerClientEvent('chat:addMessage', source, {
      args = { '^1SYSTEM', '^7Commands are disabled on this server.' }
    })
    CancelEvent()
    return
  end
  -- Only allow regular chat if the player has the koth.chat ACE.
  -- Players without this permission will see a notification and
  -- their message will be cancelled.  Commands are already
  -- blocked above.
  if not IsPlayerAceAllowed(source, 'koth.chat') then
    TriggerClientEvent('chat:addMessage', source, {
      args = { '^1SYSTEM', '^7Chat is disabled on this server.' }
    })
    CancelEvent()
  end
end)

--[[
  When a player selects a new class via the koth_classes resource,
  update their stored class level and broadcast an updated player
  data payload.  This ensures the HUD displays the correct class
  level immediately after class selection.  Without this handler the
  HUD would only update after the player earns XP.
]]
RegisterNetEvent('koth_classes:selectClass')
AddEventHandler('koth_classes:selectClass', function(classId)
  local src = source
  -- Update the player's class level in the local cache
  refreshPlayerClassLevel(src)
  -- Send updated player data to the client
  if playerData[src] then
    TriggerClientEvent('koth:updatePlayerData', src, playerData[src])
  end
end)
