-- SQL script to create the weapon ownership table for KOTH

-- This table tracks which weapons each player permanently owns for
-- each class.  The primary key is a compound of the player's TXID
-- (license), the class identifier and the weapon name.  When a
-- player purchases a weapon, it is inserted into this table using
-- INSERT IGNORE to avoid duplicates.  Owned weapons can then be
-- spawned for free in subsequent matches.

CREATE TABLE IF NOT EXISTS koth_player_owned_weapons (
  txid   VARCHAR(50) NOT NULL,
  class  VARCHAR(20) NOT NULL,
  weapon VARCHAR(50) NOT NULL,
  PRIMARY KEY (txid, class, weapon)
);