// UI Fix - Consolidated script to handle all UI interactions properly
console.log('[KOTH UI FIX] Loading consolidated UI handler...');

// Global UI state
window.UIState = {
    currentMenu: null,
    isVisible: false,
    playerData: {
        money: 0,
        level: 1,
        xp: 0
    }
};

// Hide all UI elements initially
function hideAllUI() {
    console.log('[KOTH UI FIX] Hiding all UI elements');
    
    // Hide all menu containers
    const elements = [
        '#team-select',
        '#menu-container',
        '#weapons-shop',
        '#vehicles-shop',
        '#classes-selection',
        '#weapon-shop-new',
        '#death-screen',
        '#kill-reward-popup',
        '#levelup-popup',
        '#koth-zone-status'
    ];
    
    elements.forEach(selector => {
        const el = document.querySelector(selector);
        if (el) {
            el.style.display = 'none';
            el.classList.remove('active', 'show');
        }
    });
    
    // Hide overlay
    const overlay = document.getElementById('overlay');
    if (overlay) {
        overlay.style.display = 'none';
    }
    
    window.UIState.currentMenu = null;
    window.UIState.isVisible = false;
}

// Show specific UI element
function showUI(elementId) {
    console.log('[KOTH UI FIX] Showing UI element:', elementId);
    
    hideAllUI();
    
    const element = document.getElementById(elementId);
    const overlay = document.getElementById('overlay');
    
    if (element && overlay) {
        overlay.style.display = 'block';
        element.style.display = 'block';
        element.classList.add('active', 'show');
        
        window.UIState.currentMenu = elementId;
        window.UIState.isVisible = true;
        
        // Ensure the element is visible
        element.style.opacity = '1';
        element.style.visibility = 'visible';
        element.style.pointerEvents = 'auto';
    }
}

// Main message handler
window.addEventListener('message', function(event) {
    const data = event.data;
    
    // Only log non-spam messages
    if (data.action !== 'hideKothZone' && data.action !== 'updateKothZone') {
        console.log('[KOTH UI FIX] Received message:', data.action, data);
    }
    
    switch(data.action) {
        case 'hideAll':
            hideAllUI();
            break;
            
        case 'showTeamSelect':
            showUI('team-select');
            if (data.counts) {
                updateTeamCounts(data.counts);
            }
            break;
            
        case 'showMenu':
            if (data.type === 'vehicles') {
                showVehicleShop(data);
            } else if (data.type === 'classes') {
                showClassSelection(data);
            }
            break;
            
        case 'showClassSelection':
            showClassSelection(data);
            break;
            
        case 'showWeaponSelect':
            showWeaponShop(data);
            break;
            
        case 'updatePlayerData':
            updatePlayerData(data.data);
            break;
            
        case 'showKillReward':
            showKillReward(data);
            break;
            
        case 'showDeathScreen':
            showUI('death-screen');
            updateDeathScreen(data);
            break;
            
        case 'hideDeathScreen':
            const deathScreen = document.getElementById('death-screen');
            if (deathScreen) {
                deathScreen.style.display = 'none';
            }
            break;
            
        case 'hideKothZone':
            const kothZone = document.getElementById('koth-zone-status');
            if (kothZone) {
                kothZone.style.display = 'none';
            }
            break;
            
        case 'showClassShopWithData':
            showClassSelection(data);
            break;
    }
});

// Vehicle Shop Handler
function showVehicleShop(data) {
    console.log('[KOTH UI FIX] Showing vehicle shop with data:', data);
    
    showUI('vehicles-shop');
    
    // Update money display
    const moneyEl = document.getElementById('vehicle-player-money');
    if (moneyEl && data.money !== undefined) {
        moneyEl.textContent = data.money.toLocaleString();
    }
    
    // Populate vehicles with category grouping.  We also hide VIP-only
    // vehicles unless the player is VIP (UIState.isVip).  Additionally,
    // vehicles may have a requiredLevel property; those exceeding the
    // player's level will be shown as locked.  The incoming data may
    // contain an isVip flag and playerLevel; if not provided we use
    // defaults (isVip=false, playerLevel from UIState.playerData.level).
    const grid = document.getElementById('vehicles-grid');
    if (grid && data.items) {
        // Store VIP status and player level so other UI components can honour them
        window.UIState.isVip = data.isVip || false;
        // Player level for vehicle shop; fallback to stored level
        window.UIState.vehiclePlayerLevel = data.playerLevel || window.UIState.playerData.level || 1;
        grid.innerHTML = '';
        const grouped = {};
        data.items.forEach(vehicle => {
            // VIP-only vehicles become locked if the player is not VIP
            if (vehicle.vipOnly && !window.UIState.isVip) {
                vehicle.locked = true;
            }
            // Determine lock state based on required level (in addition to VIP)
            if (vehicle.requiredLevel && vehicle.requiredLevel > window.UIState.vehiclePlayerLevel) {
                vehicle.locked = true;
            }
            const category = vehicle.category || 'Other';
            if (!grouped[category]) {
                grouped[category] = [];
            }
            grouped[category].push(vehicle);
        });
        Object.keys(grouped).forEach(cat => {
            // Insert a header for each category.  For the VIP category,
            // append "(Locked)" when the player is not VIP to clearly
            // communicate that the section is unavailable.  Use a
            // case‑insensitive comparison to handle variant casing.
            const header = document.createElement('div');
            header.className = 'vehicle-category-header';
            const isVipCategory = cat.toLowerCase() === 'vip';
            if (isVipCategory && !window.UIState.isVip) {
                header.textContent = 'VIP (Locked)';
            } else {
                header.textContent = cat;
            }
            grid.appendChild(header);
            // Insert the vehicles for this category
            grouped[cat].forEach(vehicle => {
                const vehicleCard = createVehicleCard(vehicle);
                grid.appendChild(vehicleCard);
            });
        });
    }
}

// Class Selection Handler
function showClassSelection(data) {
    console.log('[KOTH UI FIX] Showing class selection with data:', data);
    
    showUI('classes-selection');
    
    // Update player level for lock status - handle both data structures
    const playerLevel = data.playerLevel || data.level || window.UIState.playerData.level || 1;
    const playerMoney = data.money || window.UIState.playerData.money || 0;
    
    window.UIState.playerData.level = playerLevel;
    window.UIState.playerData.money = playerMoney;
    
    console.log('[KOTH UI FIX] Player level:', playerLevel, 'Money:', playerMoney);
    
    // Update class cards based on level
    const classCards = document.querySelectorAll('.class-card');
    classCards.forEach(card => {
        const className = card.dataset.class;
        const classData = getClassRequirements(className);
        
        if (classData && playerLevel < classData.requiredLevel) {
            card.classList.add('locked');
            const lockOverlay = card.querySelector('.class-lock-overlay');
            if (lockOverlay) {
                lockOverlay.style.display = 'flex';
            }
        } else {
            card.classList.remove('locked');
            const lockOverlay = card.querySelector('.class-lock-overlay');
            if (lockOverlay) {
                lockOverlay.style.display = 'none';
            }
        }
    });
}

// Weapon Shop Handler
function showWeaponShop(data) {
    console.log('[KOTH UI FIX] Showing weapon shop with data:', data);
    
    showUI('weapon-shop-new');
    
    // Update class name
    const classNameEl = document.getElementById('selected-class-name');
    if (classNameEl && data.class) {
        classNameEl.textContent = data.class.charAt(0).toUpperCase() + data.class.slice(1);
    }
    
    // Update money
    const moneyEl = document.getElementById('weapon-shop-money');
    if (moneyEl && data.money !== undefined) {
        moneyEl.textContent = data.money.toLocaleString();
    }
    
    // Populate weapons and set VIP status.  We store the player's
    // VIP status, class level and selected class name in the global
    // UI state so that the populateWeapons function can properly
    // gate items and send purchases.  The server includes both
    // `isVip` and `classLevel` in the data payload when the
    // weapon shop is shown.
    window.UIState.isVip = data.isVip || false;
    window.UIState.classLevel = data.classLevel || 0;
    // Persist the selected class and player's money for affordability
    window.UIState.selectedClass = data.class || window.UIState.selectedClass;
    if (data.money !== undefined) {
        // Update both the HUD display and internal state
        window.UIState.playerData.money = data.money;
    }
    // Update the class level display in the weapon shop header
    const levelEl = document.getElementById('weapon-shop-level');
    if (levelEl) {
        levelEl.textContent = window.UIState.classLevel;
    }
    if (data.weapons) {
        populateWeapons(data.weapons);
    }
}

// Helper functions
function createVehicleCard(vehicle) {
    const card = document.createElement('div');
    card.className = 'vehicle-card';
    if (vehicle.owned) {
        card.classList.add('owned');
    }
    if (vehicle.locked) {
        card.classList.add('locked');
    }
    
    // Determine overlay text for locked vehicles.  VIP locked vehicles display 'VIP'
    // while level-locked vehicles display the required level.
    let lockText = '';
    if (vehicle.locked) {
        if (vehicle.vipOnly && !window.UIState.isVip) {
            lockText = 'VIP';
        } else if (vehicle.requiredLevel) {
            lockText = 'LVL ' + vehicle.requiredLevel;
        } else {
            lockText = 'LOCKED';
        }
    }
    
    const vehicleImageHtml = `
        <div class="vehicle-image">
            <img src="${vehicle.img || 'images/vehicles/default.png'}" alt="${vehicle.name}">
            ${vehicle.owned ? '<div class="owned-badge">OWNED</div>' : ''}
            ${vehicle.locked ? '<div class="lock-overlay">' + lockText + '</div>' : ''}
        </div>
    `;
    
    // Determine price/rent button HTML depending on ownership and lock status
    let priceHtml = '';
    if (vehicle.owned) {
        priceHtml = '<button class="spawn-btn" data-vehicle="' + vehicle.name + '">SPAWN</button>';
    } else if (vehicle.locked) {
        // Locked vehicles display unlock info instead of buttons
        if (vehicle.vipOnly && !window.UIState.isVip) {
            priceHtml = '<div class="locked-text">VIP ONLY</div>';
        } else if (vehicle.requiredLevel) {
            priceHtml = '<div class="locked-text">Unlock at level ' + vehicle.requiredLevel + '</div>';
        } else {
            priceHtml = '<div class="locked-text">Locked</div>';
        }
    } else {
        const buyPrice = vehicle.cost ? '$' + vehicle.cost.toLocaleString() : '$0';
        const rentPrice = vehicle.rent ? '$' + vehicle.rent.toLocaleString() : '$0';
        priceHtml = `
            <button class="buy-btn" data-vehicle="${vehicle.name}" data-price="${vehicle.cost}">BUY ${buyPrice}</button>
            <button class="rent-btn" data-vehicle="${vehicle.name}" data-price="${vehicle.rent}">RENT ${rentPrice}</button>
        `;
    }
    
    card.innerHTML = `
        ${vehicleImageHtml}
        <div class="vehicle-info">
            <h3 class="vehicle-name">${vehicle.name}</h3>
            <div class="vehicle-prices">
                ${priceHtml}
            </div>
        </div>
    `;
    
    // Add click handlers only if the vehicle is not locked and not owned
    const buyBtn = card.querySelector('.buy-btn');
    const rentBtn = card.querySelector('.rent-btn');
    const spawnBtn = card.querySelector('.spawn-btn');
    
    if (buyBtn) {
        buyBtn.addEventListener('click', () => {
            fetch(`https://${GetParentResourceName()}/buyVehicle`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    name: vehicle.name,
                    price: vehicle.cost
                })
            });
        });
    }
    
    if (rentBtn) {
        rentBtn.addEventListener('click', () => {
            fetch(`https://${GetParentResourceName()}/rentVehicle`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    name: vehicle.name,
                    price: vehicle.rent
                })
            });
        });
    }
    
    if (spawnBtn) {
        spawnBtn.addEventListener('click', () => {
            fetch(`https://${GetParentResourceName()}/buyVehicle`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    name: vehicle.name,
                    price: 0
                })
            });
        });
    }
    
    return card;
}

function getClassRequirements(className) {
    const requirements = {
        assault: { requiredLevel: 1 },
        medic: { requiredLevel: 5 },
        engineer: { requiredLevel: 15 },
        heavy: { requiredLevel: 25 },
        scout: { requiredLevel: 40 }
    };
    return requirements[className];
}

function populateWeapons(weapons) {
    const grid = document.getElementById('weapon-grid');
    if (!grid) return;
    
    // Reset the grid
    grid.innerHTML = '';

    // Separate weapons by category and VIP status.  VIP weapons are
    // collected separately so they can be displayed under a dedicated
    // header.  Non‑VIP weapons are grouped by their `category`
    // property (defaults to 'other').
    const grouped = {};
    const vipItems = [];
    (weapons || []).forEach(wpn => {
        if (wpn.vipOnly) {
            vipItems.push(wpn);
        } else {
            const cat = wpn.category || 'other';
            if (!grouped[cat]) grouped[cat] = [];
            grouped[cat].push(wpn);
        }
    });

    // Helper to capitalise the first letter of category names
    const formatCat = cat => cat.charAt(0).toUpperCase() + cat.slice(1);

    // Retrieve player money and class level for affordability and lock checks
    const money = window.UIState.playerData.money || 0;
    const classLevel = window.UIState.classLevel || 0;

    // Helper to build a card for a given weapon.  This function
    // encapsulates the logic for determining locked state, price/rent
    // display, and button creation.
    function buildWeaponCard(wpn, lockedForVip, lockedForLevel) {
        const card = document.createElement('div');
        card.className = 'weapon-item';
        const isLocked = lockedForVip || lockedForLevel;
        if (isLocked) card.classList.add('locked');

        const price = Number(wpn.price) || 0;
        const rentPrice = price > 0 ? Math.floor(price * 0.3) : 0;
        const owned = wpn.owned === true;
        const canAffordBuy = money >= price;
        const canAffordRent = money >= rentPrice;

        // Determine overlay text for locked items
        let lockText = '';
        if (isLocked) {
            if (lockedForVip) {
                lockText = 'VIP';
            } else if (wpn.requiredLevel !== undefined) {
                lockText = 'LVL ' + wpn.requiredLevel;
            } else {
                lockText = 'LOCKED';
            }
        }

        // Build the inner HTML structure
        let html = '';
        html += '<div class="weapon-image">';
        html += '<img src="' + (wpn.img || 'images/guns/default.png') + '" alt="' + wpn.name + '">';
        if (isLocked) {
            html += '<div class="lock-overlay">' + lockText + '</div>';
        }
        html += '</div>';
        html += '<div class="weapon-name">' + wpn.name + '</div>';

        if (isLocked) {
            // Locked weapons: show unlock requirement instead of prices/buttons
            if (lockedForVip) {
                html += '<div class="weapon-price vip-only">VIP ONLY</div>';
            } else {
                html += '<div class="weapon-price level-locked">LVL ' + (wpn.requiredLevel || '') + '</div>';
            }
        } else if (owned || price === 0) {
            // Owned or free weapons: show FREE and a spawn button
            html += '<div class="weapon-price">FREE</div>';
            html += '<div class="weapon-buttons">';
            html += '<button class="weapon-buy-btn" data-weapon="' + wpn.weapon + '" data-price="0" data-type="buy">SPAWN</button>';
            html += '</div>';
        } else {
            // Purchasable/rentable weapons
            html += '<div class="weapon-price">$' + price.toLocaleString() + '</div>';
            html += '<div class="weapon-rent-price">Rent: $' + rentPrice.toLocaleString() + '</div>';
            html += '<div class="weapon-buttons">';
            html += '<button class="weapon-buy-btn' + (canAffordBuy ? '' : ' disabled') + '" data-weapon="' + wpn.weapon + '" data-price="' + price + '" data-type="buy" ' + (canAffordBuy ? '' : 'disabled') + '>BUY</button>';
            html += '<button class="weapon-rent-btn' + (canAffordRent ? '' : ' disabled') + '" data-weapon="' + wpn.weapon + '" data-price="' + rentPrice + '" data-type="rent" ' + (canAffordRent ? '' : 'disabled') + '>RENT</button>';
            html += '</div>';
        }

        card.innerHTML = html;

        // Attach event handlers for buy/rent/spawn actions
        if (!isLocked) {
            const buyBtn = card.querySelector('.weapon-buy-btn');
            const rentBtn = card.querySelector('.weapon-rent-btn');
            if (buyBtn && !buyBtn.classList.contains('disabled')) {
                buyBtn.addEventListener('click', () => {
                    const priceVal = Number(buyBtn.getAttribute('data-price')) || 0;
                    const type = buyBtn.getAttribute('data-type') || 'buy';
                    fetch(`https://${GetParentResourceName()}/selectWeapon`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            weapon: wpn.weapon,
                            class: window.UIState.selectedClass,
                            price: priceVal,
                            purchaseType: type
                        })
                    });
                });
            }
            if (rentBtn && !rentBtn.classList.contains('disabled')) {
                rentBtn.addEventListener('click', () => {
                    const priceVal = Number(rentBtn.getAttribute('data-price')) || 0;
                    const type = rentBtn.getAttribute('data-type') || 'rent';
                    fetch(`https://${GetParentResourceName()}/selectWeapon`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            weapon: wpn.weapon,
                            class: window.UIState.selectedClass,
                            price: priceVal,
                            purchaseType: type
                        })
                    });
                });
            }
        }

        return card;
    }

    // Render each non‑VIP category
    Object.keys(grouped).forEach(cat => {
        const header = document.createElement('div');
        header.className = 'weapon-category-header';
        header.textContent = formatCat(cat);
        grid.appendChild(header);
        grouped[cat].forEach(wpn => {
            const lockedForVip = wpn.vipOnly && !window.UIState.isVip;
            const lockedForLevel = (wpn.requiredLevel !== undefined) && (classLevel < wpn.requiredLevel) && !wpn.owned;
            const card = buildWeaponCard(wpn, lockedForVip, lockedForLevel);
            grid.appendChild(card);
        });
    });

    // Render VIP weapons section at the end
    if (vipItems.length > 0) {
        const vipHeader = document.createElement('div');
        vipHeader.className = 'weapon-category-header';
        vipHeader.textContent = window.UIState.isVip ? 'VIP' : 'VIP (Locked)';
        grid.appendChild(vipHeader);
        vipItems.forEach(wpn => {
            // For VIP items, the only lock is VIP status (level locks do not apply)
            const lockedForVip = !window.UIState.isVip;
            const lockedForLevel = false;
            const card = buildWeaponCard(wpn, lockedForVip, lockedForLevel);
            grid.appendChild(card);
        });
    }
}

function updateTeamCounts(counts) {
    if (counts.red !== undefined) {
        const redCount = document.getElementById('count-red');
        if (redCount) redCount.textContent = counts.red;
    }
    if (counts.blue !== undefined) {
        const blueCount = document.getElementById('count-blue');
        if (blueCount) blueCount.textContent = counts.blue;
    }
    if (counts.green !== undefined) {
        const greenCount = document.getElementById('count-green');
        if (greenCount) greenCount.textContent = counts.green;
    }
}

function updatePlayerData(data) {
    if (!data) return;
    
    // Update stored data
    Object.assign(window.UIState.playerData, data);
    
    // Update HUD elements
    if (data.player_name) {
        const nameEl = document.getElementById('player-name');
        if (nameEl) nameEl.textContent = data.player_name;
    }
    
    if (data.money !== undefined) {
        const moneyEl = document.getElementById('player-money-display');
        if (moneyEl) moneyEl.textContent = '$' + data.money.toLocaleString();
    }
    
    if (data.level !== undefined) {
        const levelEl = document.getElementById('player-level');
        if (levelEl) levelEl.textContent = data.level;
    }
    
    if (data.kills !== undefined) {
        const killsEl = document.getElementById('player-kills');
        if (killsEl) killsEl.textContent = data.kills;
    }

    // Update class level display if provided.  The server stores the
    // player's current class level on the `classLevel` property of
    // playerData.  When present, update the corresponding HUD element.
    if (data.classLevel !== undefined) {
        // Keep track in UIState
        window.UIState.playerData.classLevel = data.classLevel;
        const classLvlEl = document.getElementById('player-class-level');
        if (classLvlEl) classLvlEl.textContent = data.classLevel;
    }
    
    if (data.xp !== undefined) {
        updateXPBar(data.xp, data.level);
    }
}

function updateXPBar(currentXP, level) {
    const xpForNextLevel = getXPForLevel(level + 1);
    const xpForCurrentLevel = getXPForLevel(level);
    const xpProgress = currentXP - xpForCurrentLevel;
    const xpNeeded = xpForNextLevel - xpForCurrentLevel;
    const percentage = (xpProgress / xpNeeded) * 100;
    
    const xpFill = document.getElementById('xp-fill');
    const xpText = document.getElementById('xp-text');
    
    if (xpFill) xpFill.style.width = percentage + '%';
    if (xpText) xpText.textContent = `${currentXP} / ${xpForNextLevel} XP`;
}

function getXPForLevel(level) {
    const levels = [0, 100, 250, 500, 1000, 1750, 2750, 4000, 6000, 8500];
    return levels[level - 1] || (8500 + (level - 10) * 2000);
}

function showKillReward(data) {
    const popup = document.getElementById('kill-reward-popup');
    if (!popup) return;
    
    // Update values
    const moneyEl = document.getElementById('kill-reward-value');
    const xpEl = document.getElementById('kill-xp-value');
    const zoneEl = document.getElementById('kill-reward-zone-indicator');
    
    if (moneyEl) moneyEl.textContent = '$' + (data.money || 50);
    if (xpEl) xpEl.textContent = data.xp || 50;
    if (zoneEl) zoneEl.style.display = data.inZone ? 'block' : 'none';
    
    // Show popup
    popup.style.display = 'block';
    popup.classList.add('show');
    
    // Hide after 3 seconds
    setTimeout(() => {
        popup.classList.remove('show');
        setTimeout(() => {
            popup.style.display = 'none';
        }, 300);
    }, 3000);
}

function updateDeathScreen(data) {
    if (data.killer) {
        const killerNameEl = document.getElementById('killer-name');
        if (killerNameEl) killerNameEl.textContent = data.killer;
    }
    
    if (data.killerId) {
        const killerIdEl = document.getElementById('killer-id');
        if (killerIdEl) killerIdEl.textContent = data.killerId;
    }
}

// Initialize close buttons
document.addEventListener('DOMContentLoaded', function() {
    console.log('[KOTH UI FIX] DOM loaded, initializing handlers...');
    
    // Hide all UI initially
    hideAllUI();
    
    // Close button handlers
    const closeButtons = [
        { selector: '#close-btn', action: 'closeMenu' },
        { selector: '#shop-close', action: 'closeMenu' },
        { selector: '#vehicle-shop-close', action: 'closeMenu' },
        { selector: '#classes-close', action: 'closeMenu' },
        { selector: '#weapon-shop-close', action: 'closeMenu' }
    ];
    
    closeButtons.forEach(btn => {
        const element = document.querySelector(btn.selector);
        if (element) {
            element.addEventListener('click', () => {
                hideAllUI();
                fetch(`https://${GetParentResourceName()}/${btn.action}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({})
                });
            });
        }
    });
    
    // Team selection handlers
    const teamCards = document.querySelectorAll('.team-card');
    teamCards.forEach(card => {
        card.addEventListener('click', function() {
            const team = this.dataset.team;
            fetch(`https://${GetParentResourceName()}/selectTeam`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ team: team })
            });
        });
    });
    
    // Class selection handlers
    const classCards = document.querySelectorAll('.class-card');
    classCards.forEach(card => {
        card.addEventListener('click', function() {
            if (!this.classList.contains('locked')) {
                const classId = this.dataset.class;
                window.UIState.selectedClass = classId;
                fetch(`https://${GetParentResourceName()}/selectClass`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ id: classId })
                });
            }
        });
    });
    
    // Weapon category handlers
    const categoryButtons = document.querySelectorAll('.weapon-category');
    categoryButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            categoryButtons.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            // Filter weapons by category (implement if needed)
        });
    });
    
    // Show HUD
    const gameHud = document.getElementById('game-hud');
    if (gameHud) {
        gameHud.style.display = 'block';
    }
});

// ESC key handler
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape' && window.UIState.isVisible) {
        hideAllUI();
        fetch(`https://${GetParentResourceName()}/closeMenu`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({})
        });
    }
});

console.log('[KOTH UI FIX] UI handler loaded successfully');
