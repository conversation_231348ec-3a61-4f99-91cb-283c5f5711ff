-- Test UI Interaction Script
-- This script tests if the UI opens properly when pressing E on peds

--[[
  Test command to simulate pressing E on the class menu ped.  Disabled for
  production to prevent exposing unnecessary commands to players.
]]
-- RegisterCommand('testclassui', function()
--     print('[UI TEST] Simulating class menu interaction...')
--     
--     -- Trigger the event that happens when pressing E on the class ped
--     TriggerEvent('koth:openClassMenu')
--     
--     print('[UI TEST] Class menu event triggered')
-- end, false)

--[[
  Test command to simulate pressing E on the vehicle menu ped.  Disabled for
  production.
]]
-- RegisterCommand('testvehicleui', function()
--     print('[UI TEST] Simulating vehicle menu interaction...')
--     
--     -- Trigger the event that happens when pressing E on the vehicle ped
--     TriggerEvent('koth:openVehicleMenu')
--     
--     print('[UI TEST] Vehicle menu event triggered')
-- end, false)

--[[
  Test command to check if the UI is visible.  Disabled for production.
]]
-- RegisterCommand('checkui', function()
--     print('[UI TEST] Checking UI state...')
--     
--     -- Send a test message to check if <PERSON><PERSON> is responding
--     SendNUIMessage({
--         action = 'test',
--         message = 'UI visibility test'
--     })
--     
--     print('[UI TEST] Test message sent to NUI')
-- end, false)

--[[
  Callback used during UI testing to verify NUI responses.  Disabled for production.
]]
-- RegisterNUICallback('test_response', function(data, cb)
--     print('[UI TEST] NUI responded! UI is working properly')
--     cb('ok')
-- end)

--[[
  Test the full UI interaction flow.  Disabled for production.
]]
-- RegisterCommand('testfullui', function()
--     print('[UI TEST] Starting full UI interaction test...')
--     
--     -- First, ensure player has data
--     TriggerServerEvent('koth:requestPlayerData')
--     
--     Citizen.Wait(1000)
--     
--     -- Test class menu
--     print('[UI TEST] Testing class menu...')
--     TriggerEvent('koth:openClassMenu')
--     
--     Citizen.Wait(3000)
--     
--     -- Close menu
--     SendNUIMessage({ action = 'hideAll' })
--     SetNuiFocus(false, false)
--     
--     Citizen.Wait(1000)
--     
--     -- Test vehicle menu
--     print('[UI TEST] Testing vehicle menu...')
--     TriggerEvent('koth:openVehicleMenu')
--     
--     Citizen.Wait(3000)
--     
--     -- Close menu
--     SendNUIMessage({ action = 'hideAll' })
--     SetNuiFocus(false, false)
--     
--     print('[UI TEST] Full UI test completed')
-- end, false)

--[[
  Thread used during UI testing to draw debug output above NPCs.  Disabled for production.
]]
-- Citizen.CreateThread(function()
--     while true do
--         local playerPed = PlayerPedId()
--         local playerCoords = GetEntityCoords(playerPed)
--         
--         -- Check for nearby peds (from spawnedPeds table if available)
--         for _, ped in ipairs(GetGamePool('CPed')) do
--             if DoesEntityExist(ped) and not IsPedAPlayer(ped) then
--                 local pedCoords = GetEntityCoords(ped)
--                 local distance = #(playerCoords - pedCoords)
--                 
--                 if distance < 2.0 then
--                     -- Draw debug text
--                     local onScreen, _x, _y = World3dToScreen2d(pedCoords.x, pedCoords.y, pedCoords.z + 1.0)
--                     if onScreen then
--                         SetTextScale(0.35, 0.35)
--                         SetTextFont(4)
--                         SetTextCentre(true)
--                         SetTextColour(0, 255, 0, 215)
--                         SetTextEntry("STRING")
--                         AddTextComponentString("Press E - Distance: " .. string.format("%.2f", distance))
--                         DrawText(_x, _y)
--                     end
--                 end
--             end
--         end
--         
--         Citizen.Wait(0)
--     end
-- end)

--[[
  Informative prints used during UI testing.  Disabled for production.
]]
-- print('[UI TEST] UI interaction test script loaded')
-- print('[UI TEST] Commands available:')
-- print('  /testclassui - Test class selection UI')
-- print('  /testvehicleui - Test vehicle shop UI')
-- print('  /checkui - Check if UI is responding')
-- print('  /testfullui - Run full UI test sequence')
