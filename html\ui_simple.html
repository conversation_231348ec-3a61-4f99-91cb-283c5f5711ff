<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>KOTH UI</title>
  <link rel="stylesheet" href="style.css">
</head>
<body>
  <div id="overlay">
    <!-- Enhanced Team Select Screen -->
    <div id="team-select">
      <div class="team-select-container">
        <div class="team-select-header">
          <h1 class="team-select-title">Choose Your Team</h1>
          <div class="team-select-subtitle">Select a team to join the battle</div>
        </div>
        
        <div class="team-buttons-container">
          <div class="team-card" data-team="red">
            <div class="team-card-inner">
              <div class="team-color-indicator red-indicator"></div>
              <div class="team-info">
                <div class="team-name">RED TEAM</div>
                <div class="team-count-display">
                  <span class="team-count" id="count-red">0</span>
                  <span class="team-count-label">PLAYERS</span>
                </div>
              </div>
              <div class="team-select-btn">JOIN</div>
            </div>
          </div>

          <div class="team-card" data-team="blue">
            <div class="team-card-inner">
              <div class="team-color-indicator blue-indicator"></div>
              <div class="team-info">
                <div class="team-name">BLUE TEAM</div>
                <div class="team-count-display">
                  <span class="team-count" id="count-blue">0</span>
                  <span class="team-count-label">PLAYERS</span>
                </div>
              </div>
              <div class="team-select-btn">JOIN</div>
            </div>
          </div>

          <div class="team-card" data-team="green">
            <div class="team-card-inner">
              <div class="team-color-indicator green-indicator"></div>
              <div class="team-info">
                <div class="team-name">GREEN TEAM</div>
                <div class="team-count-display">
                  <span class="team-count" id="count-green">0</span>
                  <span class="team-count-label">PLAYERS</span>
                </div>
              </div>
              <div class="team-select-btn">JOIN</div>
            </div>
          </div>
        </div>

        <div class="team-select-footer">
          <div class="loading-indicator">
            <div class="loading-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
            <div class="loading-text">Loading team data...</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Dynamic Menus: Vehicles & Classes -->
    <div id="menu-container">
      <button id="close-btn">×</button>
      <h2 id="menu-title"></h2>
      <div id="items"></div>
    </div>

    <!-- Weapons Shop UI -->
    <div id="weapons-shop">
      <div class="shop-header">
        <div class="shop-title">WEAPONS SHOP</div>
        <!-- Display the player's current class level in the weapon shop header.  We add
             a label above the level to explicitly indicate that it refers to the
             class level. -->
        <div class="shop-level">
          <div class="shop-class-level-label">Class Level</div>
          LVL <span id="weapon-shop-level">0</span>
        </div>
        <div class="shop-money">$<span id="player-money">7000</span></div>
      </div>

      <!-- Class XP progress bar in weapon shop.  This appears below the
           header and shows progress towards the next class level. -->
      <div class="class-xp-progress" id="shop-class-xp">
        <div class="class-xp-bar">
          <div class="class-xp-fill" id="shop-class-xp-fill"></div>
        </div>
        <div class="class-xp-text" id="shop-class-xp-text">0 / 0 XP</div>
      </div>

      <div class="shop-search">
        <input type="text" id="weapon-search" placeholder="Search for an item">
        <button class="refresh-btn">🔄</button>
      </div>

      <div class="weapons-grid" id="weapons-grid">
        <!-- Weapons will be populated here -->
      </div>

      <button class="shop-close" id="shop-close">×</button>
    </div>

    <!-- Vehicles Shop UI -->
    <div id="vehicles-shop">
      <div class="shop-header">
        <div class="shop-title">VEHICLES SHOP</div>
        <div class="shop-money">$<span id="vehicle-player-money">7000</span></div>
      </div>

      <div class="shop-search">
        <input type="text" id="vehicle-search" placeholder="Search for a vehicle">
        <button class="refresh-btn">🔄</button>
      </div>

      <div class="vehicles-grid" id="vehicles-grid">
        <!-- Vehicles will be populated here -->
      </div>

      <button class="shop-close" id="vehicle-shop-close">×</button>
    </div>

    <!-- NEW Classes Selection UI - Matching Your Design -->
    <div id="classes-selection">
      <div class="classes-header">
        <h2 class="classes-title">Select a class</h2>
        <div class="classes-money">$<span id="classes-player-money">0</span></div>
        <button class="classes-close" id="classes-close">×</button>
      </div>

      <div class="classes-grid">
        <div class="class-card" data-class="assault">
          <div class="class-image-container">
            <img src="images/classes/assault.png" alt="Assault" class="class-image">
          </div>
          <div class="class-info">
            <h3 class="class-name">Assault</h3>
            <p class="class-unlock">Unlocked</p>
          </div>
        </div>

        <div class="class-card" data-class="medic">
          <div class="class-image-container">
            <img src="images/classes/medic.png" alt="Medic" class="class-image">
            <div class="class-lock-overlay">
              <span class="lock-icon">🔒</span>
            </div>
          </div>
          <div class="class-info">
            <h3 class="class-name">Medic</h3>
            <p class="class-unlock">Unlock at level 5</p>
          </div>
        </div>

        <div class="class-card" data-class="engineer">
          <div class="class-image-container">
            <img src="images/classes/engineer.png" alt="Engineer" class="class-image">
            <div class="class-lock-overlay">
              <span class="lock-icon">🔒</span>
            </div>
          </div>
          <div class="class-info">
            <h3 class="class-name">Engineer</h3>
            <p class="class-unlock">Unlock at level 15</p>
          </div>
        </div>

        <div class="class-card" data-class="heavy">
          <div class="class-image-container">
            <img src="images/classes/heavy.png" alt="Heavy" class="class-image">
            <div class="class-lock-overlay">
              <span class="lock-icon">🔒</span>
            </div>
          </div>
          <div class="class-info">
            <h3 class="class-name">Heavy</h3>
            <p class="class-unlock">Unlock at level 25</p>
          </div>
        </div>

        <div class="class-card" data-class="scout">
          <div class="class-image-container">
            <img src="images/classes/scout.png" alt="Scout" class="class-image">
            <div class="class-lock-overlay">
              <span class="lock-icon">🔒</span>
            </div>
          </div>
          <div class="class-info">
            <h3 class="class-name">Scout</h3>
            <p class="class-unlock">Unlock at level 40</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Map vote panel overlay.  This UI appears at the end of a round when
         the server requests players to vote on the next map.  Three
         candidate maps are displayed as buttons that players can click
         to cast their vote.  A countdown timer shows how much time
         remains before the vote ends.  Hidden by default. -->
    <div id="map-vote-panel" class="map-vote-panel" style="display: none;">
      <h2 class="map-vote-title">Vote for the next map</h2>
      <div id="map-vote-options" class="map-vote-options"></div>
      <div id="map-vote-timer" class="map-vote-timer">Time left: 30s</div>
    </div>

    <!-- NEW Weapon Shop UI - Matching Your Design -->
    <div id="weapon-shop-new">
      <div class="weapon-shop-header">
        <h2 class="weapon-shop-title"><span id="selected-class-name">Assault</span> Class Shop</h2>
        <div class="weapon-shop-money">$ <span id="weapon-shop-money">2,100</span></div>
        <button class="weapon-shop-close" id="weapon-shop-close">×</button>
      </div>

      <div class="weapon-categories">
        <button class="weapon-category active" data-category="primary">Primary</button>
        <button class="weapon-category" data-category="secondary">Secondary</button>
        <button class="weapon-category" data-category="special">Special</button>
        <button class="weapon-category" data-category="throwable">Throwable</button>
        <!-- VIP category button.  Visible for all players but will only
             contain items for those with the VIP role.  The UI script
             will hide VIP-only items automatically if the player does
             not have access. -->
        <!-- Prestige category button.  This corresponds to items flagged as VIP in the data, but
             we repurpose the VIP category to represent Prestige weapons.  The
             data-category remains 'vip' to keep compatibility with the script logic. -->
        <button class="weapon-category" data-category="vip">Prestige</button>
      </div>

      <div class="weapon-grid" id="weapon-grid">
        <!-- Weapons will be dynamically populated here -->
      </div>
    </div>

    <!-- ATTACHMENT SHOP UI -->
    <div id="attachment-shop">
      <div class="attachment-shop-header">
        <h2 class="attachment-shop-title">Weapon Attachments</h2>
        <div class="attachment-shop-money">$<span id="attachment-player-money">0</span></div>
        <button class="attachment-shop-close" id="attachment-shop-close">×</button>
      </div>

      <div class="attachment-weapon-info">
        <div class="attachment-weapon-name" id="attachment-weapon-name">Current Weapon</div>
        <!-- Display the updated attachment price.  Each attachment now costs $50
             instead of $150.  This value is controlled server-side but
             echoed here for consistency. -->
        <div class="attachment-price-info">All attachments: $50 each</div>
      </div>

      <div class="attachment-grid" id="attachment-grid">
        <!-- Attachments will be populated here -->
      </div>
    </div>
  </div>

  <!-- Kill Feed Container -->
  <!--
      This container holds the on‑screen kill feed entries.  Messages are
      populated via NUI messages in script.js (action 'killFeed').  Each
      entry shows which player killed which with team colour coding.  The
      container is positioned near the top‑right of the screen and does
      not block any interactive UI elements.
  -->
  <div id="kill-feed-container" class="kill-feed-container"></div>

  <!-- Prestige Menu Overlay -->
  <!--
      This overlay provides a simple prestige shop.  It appears when the
      player interacts with the prestige ped.  The menu shows the
      player's prestige rank and available weapon/vehicle tokens.  Players
      can choose to prestige (resetting their level) or spend tokens to
      unlock a prestige weapon or the prestige vehicle.  Buttons and
      selectors will be enabled/disabled by script.js based on the
      player's level and token counts.  The menu is hidden by default.
  -->
  <div id="prestige-menu" class="prestige-menu" style="display: none;">
    <div class="prestige-menu-content">
      <h2 class="prestige-title">Prestige Shop</h2>
      <p class="prestige-info">Prestige Rank: <span id="prestige-rank">0</span></p>
      <p class="prestige-info">Weapon Tokens: <span id="prestige-weapon-tokens">0</span></p>
      <p class="prestige-info">Vehicle Tokens: <span id="prestige-vehicle-tokens">0</span></p>
      <div id="prestige-actions" class="prestige-actions">
        <!-- Prestige button; disabled when player is below level 50 -->
        <button id="prestige-button" class="prestige-btn">Prestige</button>
        <!-- Weapon selection section; shown when unlocking a weapon -->
        <div id="prestige-weapon-section" style="margin-top: 10px; display: none;">
          <p>Select a prestige weapon:</p>
          <div id="prestige-weapons-list" class="prestige-weapons-list"></div>
        </div>
        <!-- Vehicle selection section; shown when unlocking a vehicle -->
        <div id="prestige-vehicle-section" style="margin-top: 10px; display: none;">
          <p>Select a prestige vehicle:</p>
          <div id="prestige-vehicles-list" class="prestige-vehicles-list"></div>
        </div>
        <!-- Primary actions -->
        <button id="prestige-buy-weapon-button" class="prestige-btn" style="margin-top: 10px;">Unlock Prestige Weapon</button>
        <button id="prestige-buy-vehicle-button" class="prestige-btn" style="margin-top: 10px;">Unlock Prestige Vehicle</button>
        <button id="prestige-close-button" class="prestige-btn" style="margin-top: 10px;">Close</button>
      </div>
    </div>
  </div>

  <!-- PERMANENT GAME HUD - COMPACT GREY DESIGN (SMALLER) -->
  <div id="game-hud">
    <!-- Zone Control Points (Top Section) -->
    <div class="zone-points">
      <div class="zone-box red-zone">
        <div class="zone-number" id="red-zone-points">0</div>
        <div class="zone-label">RED</div>
      </div>

      <div class="zone-box green-zone">
        <div class="zone-number" id="green-zone-points">0</div>
        <div class="zone-label">GREEN</div>
      </div>

      <div class="zone-box blue-zone">
        <div class="zone-number" id="blue-zone-points">0</div>
        <div class="zone-label">BLUE</div>
      </div>
    </div>

    <!-- Player Info Section -->
    <div class="player-info">
      <div class="player-name" id="player-name">andyflip9</div>
      <div class="player-money" id="player-money-display">$49,235</div>
      <div class="player-stats">
        <div class="stat-item">
          <!-- Display the player's level.  This label has been reverted
               back to "Level" to differentiate from the class level shown
               in the weapon shop. -->
          <div class="stat-label">Level</div>
          <div class="stat-value" id="player-level">41</div>
        </div>
        <!-- Replace the Kills statistic with a Class Level statistic.
             The class level is updated dynamically via the UI script
             whenever the player gains XP or selects a new class. -->
        <div class="stat-item">
          <div class="stat-label">Kills</div>
          <!-- Display the player's kill count here.  The ID has been
               renamed from player-class-level to player-kills to
               reflect its new purpose. -->
          <div class="stat-value" id="player-kills">0</div>
        </div>

        <!-- Prestige statistic.  Shows how many times the player has
             prestiged.  Updated dynamically via the updatePlayerData
             message in script.js. -->
        <div class="stat-item">
          <div class="stat-label">Prestige</div>
          <div class="stat-value" id="player-prestige">0</div>
        </div>
      </div>

      <!-- XP Progress Bar -->
      <div class="xp-progress">
        <div class="xp-bar">
          <div class="xp-fill" id="xp-fill" style="width: 63%;"></div>
        </div>
        <div class="xp-text" id="xp-text">2847 / 4500 XP</div>
      </div>

      <!-- Removed class XP bar from the main HUD at the user's request.  The
           class XP progress is now shown only in the weapon shop UI. -->

      <!-- Team Player Counts -->
      <div class="team-player-counts">
        <div class="team-players-item red">
          <span class="team-dot red-dot"></span>
          <span class="team-count-text" id="red-players">0</span>
        </div>
        <div class="team-players-item green">
          <span class="team-dot green-dot"></span>
          <span class="team-count-text" id="green-players">0</span>
        </div>
        <div class="team-players-item blue">
          <span class="team-dot blue-dot"></span>
          <span class="team-count-text" id="blue-players">0</span>
        </div>
      </div>
    </div>

    <!-- Health Bar REMOVED as requested - user will integrate their own later -->

    <!-- KOTH Zone Status (When Active) -->
    <div class="koth-zone-status" id="koth-zone-status">
      <div class="koth-zone-header">
        <span class="koth-crown">👑</span>
        <span class="koth-zone-name">QUARRY ZONE</span>
      </div>
      <div class="koth-progress">
        <div class="koth-progress-bar">
          <div class="koth-progress-fill" id="koth-progress-fill"></div>
        </div>
        <div class="koth-progress-text" id="koth-progress-text">Neutral</div>
      </div>
    </div>
  </div>

  <!-- DEATH SCREEN -->
  <div id="death-screen">
    <div class="death-content">
      <!-- Updated death message to be less aggressive -->
      <div class="death-title">You Died</div>

      <div class="respawn-section">
        <div class="respawn-instruction">HOLD <span class="key-highlight">E</span> TO RESPAWN</div>
        <div class="respawn-progress">
          <div class="respawn-bar">
            <div class="respawn-fill" id="respawn-fill"></div>
          </div>
        </div>
      </div>

      <div class="bleedout-section">
        <div class="bleedout-text">BLEEDING OUT IN <span id="bleedout-timer">50</span> SECONDS!</div>
        <div class="medic-call">QUICK! Shout <span class="medic-highlight">MEDIC!</span></div>
      </div>

      <div class="killer-info">
        <div class="killer-text">Killed by: <span class="killer-id">[<span id="killer-id">463426</span>]</span> <span class="killer-name" id="killer-name">Jeon Kim</span></div>
      </div>
    </div>
  </div>

  <!-- ENHANCED KILL REWARD POPUP -->
  <div id="kill-reward-popup">
    <div class="kill-reward-content">
      <div class="kill-reward-header">
        <div class="kill-reward-plus">+</div>
        <div class="kill-reward-money" id="kill-reward-value">$350</div>
        <div class="kill-reward-confirmed">Kill confirmed</div>
      </div>
      <div class="kill-reward-details">
        <div class="kill-reward-xp">
          <span class="xp-plus">+</span>
          <span class="xp-amount" id="kill-xp-value">300</span>
          <span class="xp-label">xp</span>
        </div>
        <div class="kill-reward-zone" id="kill-reward-zone-indicator" style="display: none;">
          <span class="zone-text">ZONE KILL BONUS!</span>
        </div>
      </div>
    </div>
  </div>

  <!-- LEVEL UP POPUP -->
  <div id="levelup-popup">
    <div class="levelup-content-small">
      <div class="levelup-header">
        <span class="levelup-icon">⭐</span>
        <span class="levelup-title">LEVEL UP!</span>
      </div>
      <div class="levelup-progress">
        <span class="level-old" id="level-old">1</span>
        <span class="level-arrow">→</span>
        <span class="level-new" id="level-new">2</span>
      </div>
    </div>
  </div>

  <!-- WEAPON HOTBAR REMOVED - Using separate hotbar resource instead -->

  <!-- Only load the necessary scripts -->
  <!-- Load the main UI script.  This script handles all UI
       interactions including team selection, class selection,
       vehicle and weapon shops, attachments and the HUD.  It has
       been updated to incorporate the new vehicle unlock
       progression and weapon buy/rent logic. -->
  <script src="script.js"></script>
</body>
</html>
